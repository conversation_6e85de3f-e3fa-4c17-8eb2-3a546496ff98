{"nbformat": 4, "nbformat_minor": 0, "metadata": {"colab": {"provenance": [], "gpuType": "T4"}, "kernelspec": {"name": "python3", "display_name": "Python 3"}, "language_info": {"name": "python"}, "accelerator": "GPU"}, "cells": [{"cell_type": "code", "source": ["!pip install roboflow\n", "!pip install ultralytics"], "metadata": {"id": "I5_rUx7HZcV5", "colab": {"base_uri": "https://localhost:8080/"}, "collapsed": true, "outputId": "950b3536-0aa6-4fa6-9595-4b43d8fde752"}, "execution_count": 1, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Collecting roboflow\n", "  Downloading roboflow-1.1.66-py3-none-any.whl.metadata (9.7 kB)\n", "Requirement already satisfied: certifi in /usr/local/lib/python3.11/dist-packages (from roboflow) (2025.6.15)\n", "Collecting idna==3.7 (from roboflow)\n", "  Downloading idna-3.7-py3-none-any.whl.metadata (9.9 kB)\n", "Requirement already satisfied: cycler in /usr/local/lib/python3.11/dist-packages (from roboflow) (0.12.1)\n", "Requirement already satisfied: kiwisolver>=1.3.1 in /usr/local/lib/python3.11/dist-packages (from roboflow) (1.4.8)\n", "Requirement already satisfied: matplotlib in /usr/local/lib/python3.11/dist-packages (from roboflow) (3.10.0)\n", "Requirement already satisfied: numpy>=1.18.5 in /usr/local/lib/python3.11/dist-packages (from roboflow) (2.0.2)\n", "Collecting opencv-python-headless==********* (from roboflow)\n", "  Downloading opencv_python_headless-*********-cp37-abi3-manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (20 kB)\n", "Requirement already satisfied: Pillow>=7.1.2 in /usr/local/lib/python3.11/dist-packages (from roboflow) (11.2.1)\n", "Collecting pillow-heif>=0.18.0 (from roboflow)\n", "  Downloading pillow_heif-0.22.0-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl.metadata (9.6 kB)\n", "Requirement already satisfied: python-dateutil in /usr/local/lib/python3.11/dist-packages (from roboflow) (2.9.0.post0)\n", "Collecting python-dotenv (from roboflow)\n", "  Downloading python_dotenv-1.1.1-py3-none-any.whl.metadata (24 kB)\n", "Requirement already satisfied: requests in /usr/local/lib/python3.11/dist-packages (from roboflow) (2.32.3)\n", "Requirement already satisfied: six in /usr/local/lib/python3.11/dist-packages (from roboflow) (1.17.0)\n", "Requirement already satisfied: urllib3>=1.26.6 in /usr/local/lib/python3.11/dist-packages (from roboflow) (2.4.0)\n", "Requirement already satisfied: tqdm>=4.41.0 in /usr/local/lib/python3.11/dist-packages (from roboflow) (4.67.1)\n", "Requirement already satisfied: PyYAML>=5.3.1 in /usr/local/lib/python3.11/dist-packages (from roboflow) (6.0.2)\n", "Requirement already satisfied: requests-toolbelt in /usr/local/lib/python3.11/dist-packages (from roboflow) (1.0.0)\n", "Collecting filetype (from roboflow)\n", "  Downloading filetype-1.2.0-py2.py3-none-any.whl.metadata (6.5 kB)\n", "Requirement already satisfied: contourpy>=1.0.1 in /usr/local/lib/python3.11/dist-packages (from matplotlib->roboflow) (1.3.2)\n", "Requirement already satisfied: fonttools>=4.22.0 in /usr/local/lib/python3.11/dist-packages (from matplotlib->roboflow) (4.58.4)\n", "Requirement already satisfied: packaging>=20.0 in /usr/local/lib/python3.11/dist-packages (from matplotlib->roboflow) (24.2)\n", "Requirement already satisfied: pyparsing>=2.3.1 in /usr/local/lib/python3.11/dist-packages (from matplotlib->roboflow) (3.2.3)\n", "Requirement already satisfied: charset-normalizer<4,>=2 in /usr/local/lib/python3.11/dist-packages (from requests->roboflow) (3.4.2)\n", "Downloading roboflow-1.1.66-py3-none-any.whl (86 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m86.7/86.7 kB\u001b[0m \u001b[31m6.4 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading idna-3.7-py3-none-any.whl (66 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m66.8/66.8 kB\u001b[0m \u001b[31m6.1 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading opencv_python_headless-*********-cp37-abi3-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (49.9 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m49.9/49.9 MB\u001b[0m \u001b[31m19.9 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading pillow_heif-0.22.0-cp311-cp311-manylinux_2_17_x86_64.manylinux2014_x86_64.whl (7.8 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m7.8/7.8 MB\u001b[0m \u001b[31m127.1 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading filetype-1.2.0-py2.py3-none-any.whl (19 kB)\n", "Downloading python_dotenv-1.1.1-py3-none-any.whl (20 kB)\n", "Installing collected packages: filetype, python-dotenv, pillow-heif, opencv-python-headless, idna, roboflow\n", "  Attempting uninstall: opencv-python-headless\n", "    Found existing installation: opencv-python-headless *********\n", "    Uninstalling opencv-python-headless-*********:\n", "      Successfully uninstalled opencv-python-headless-*********\n", "  Attempting uninstall: idna\n", "    Found existing installation: idna 3.10\n", "    Uninstalling idna-3.10:\n", "      Successfully uninstalled idna-3.10\n", "Successfully installed filetype-1.2.0 idna-3.7 opencv-python-headless-********* pillow-heif-0.22.0 python-dotenv-1.1.1 roboflow-1.1.66\n", "Collecting ultralytics\n", "  Downloading ultralytics-8.3.159-py3-none-any.whl.metadata (37 kB)\n", "Requirement already satisfied: numpy>=1.23.0 in /usr/local/lib/python3.11/dist-packages (from ultralytics) (2.0.2)\n", "Requirement already satisfied: matplotlib>=3.3.0 in /usr/local/lib/python3.11/dist-packages (from ultralytics) (3.10.0)\n", "Requirement already satisfied: opencv-python>=4.6.0 in /usr/local/lib/python3.11/dist-packages (from ultralytics) (*********)\n", "Requirement already satisfied: pillow>=7.1.2 in /usr/local/lib/python3.11/dist-packages (from ultralytics) (11.2.1)\n", "Requirement already satisfied: pyyaml>=5.3.1 in /usr/local/lib/python3.11/dist-packages (from ultralytics) (6.0.2)\n", "Requirement already satisfied: requests>=2.23.0 in /usr/local/lib/python3.11/dist-packages (from ultralytics) (2.32.3)\n", "Requirement already satisfied: scipy>=1.4.1 in /usr/local/lib/python3.11/dist-packages (from ultralytics) (1.15.3)\n", "Requirement already satisfied: torch>=1.8.0 in /usr/local/lib/python3.11/dist-packages (from ultralytics) (2.6.0+cu124)\n", "Requirement already satisfied: torchvision>=0.9.0 in /usr/local/lib/python3.11/dist-packages (from ultralytics) (0.21.0+cu124)\n", "Requirement already satisfied: tqdm>=4.64.0 in /usr/local/lib/python3.11/dist-packages (from ultralytics) (4.67.1)\n", "Requirement already satisfied: psutil in /usr/local/lib/python3.11/dist-packages (from ultralytics) (5.9.5)\n", "Requirement already satisfied: py-cpuinfo in /usr/local/lib/python3.11/dist-packages (from ultralytics) (9.0.0)\n", "Requirement already satisfied: pandas>=1.1.4 in /usr/local/lib/python3.11/dist-packages (from ultralytics) (2.2.2)\n", "Collecting ultralytics-thop>=2.0.0 (from ultralytics)\n", "  Downloading ultralytics_thop-2.0.14-py3-none-any.whl.metadata (9.4 kB)\n", "Requirement already satisfied: contourpy>=1.0.1 in /usr/local/lib/python3.11/dist-packages (from matplotlib>=3.3.0->ultralytics) (1.3.2)\n", "Requirement already satisfied: cycler>=0.10 in /usr/local/lib/python3.11/dist-packages (from matplotlib>=3.3.0->ultralytics) (0.12.1)\n", "Requirement already satisfied: fonttools>=4.22.0 in /usr/local/lib/python3.11/dist-packages (from matplotlib>=3.3.0->ultralytics) (4.58.4)\n", "Requirement already satisfied: kiwisolver>=1.3.1 in /usr/local/lib/python3.11/dist-packages (from matplotlib>=3.3.0->ultralytics) (1.4.8)\n", "Requirement already satisfied: packaging>=20.0 in /usr/local/lib/python3.11/dist-packages (from matplotlib>=3.3.0->ultralytics) (24.2)\n", "Requirement already satisfied: pyparsing>=2.3.1 in /usr/local/lib/python3.11/dist-packages (from matplotlib>=3.3.0->ultralytics) (3.2.3)\n", "Requirement already satisfied: python-dateutil>=2.7 in /usr/local/lib/python3.11/dist-packages (from matplotlib>=3.3.0->ultralytics) (2.9.0.post0)\n", "Requirement already satisfied: pytz>=2020.1 in /usr/local/lib/python3.11/dist-packages (from pandas>=1.1.4->ultralytics) (2025.2)\n", "Requirement already satisfied: tzdata>=2022.7 in /usr/local/lib/python3.11/dist-packages (from pandas>=1.1.4->ultralytics) (2025.2)\n", "Requirement already satisfied: charset-normalizer<4,>=2 in /usr/local/lib/python3.11/dist-packages (from requests>=2.23.0->ultralytics) (3.4.2)\n", "Requirement already satisfied: idna<4,>=2.5 in /usr/local/lib/python3.11/dist-packages (from requests>=2.23.0->ultralytics) (3.7)\n", "Requirement already satisfied: urllib3<3,>=1.21.1 in /usr/local/lib/python3.11/dist-packages (from requests>=2.23.0->ultralytics) (2.4.0)\n", "Requirement already satisfied: certifi>=2017.4.17 in /usr/local/lib/python3.11/dist-packages (from requests>=2.23.0->ultralytics) (2025.6.15)\n", "Requirement already satisfied: filelock in /usr/local/lib/python3.11/dist-packages (from torch>=1.8.0->ultralytics) (3.18.0)\n", "Requirement already satisfied: typing-extensions>=4.10.0 in /usr/local/lib/python3.11/dist-packages (from torch>=1.8.0->ultralytics) (4.14.0)\n", "Requirement already satisfied: networkx in /usr/local/lib/python3.11/dist-packages (from torch>=1.8.0->ultralytics) (3.5)\n", "Requirement already satisfied: jinja2 in /usr/local/lib/python3.11/dist-packages (from torch>=1.8.0->ultralytics) (3.1.6)\n", "Requirement already satisfied: fsspec in /usr/local/lib/python3.11/dist-packages (from torch>=1.8.0->ultralytics) (2025.3.2)\n", "Collecting nvidia-cuda-nvrtc-cu12==12.4.127 (from torch>=1.8.0->ultralytics)\n", "  Downloading nvidia_cuda_nvrtc_cu12-12.4.127-py3-none-manylinux2014_x86_64.whl.metadata (1.5 kB)\n", "Collecting nvidia-cuda-runtime-cu12==12.4.127 (from torch>=1.8.0->ultralytics)\n", "  Downloading nvidia_cuda_runtime_cu12-12.4.127-py3-none-manylinux2014_x86_64.whl.metadata (1.5 kB)\n", "Collecting nvidia-cuda-cupti-cu12==12.4.127 (from torch>=1.8.0->ultralytics)\n", "  Downloading nvidia_cuda_cupti_cu12-12.4.127-py3-none-manylinux2014_x86_64.whl.metadata (1.6 kB)\n", "Collecting nvidia-cudnn-cu12==9.1.0.70 (from torch>=1.8.0->ultralytics)\n", "  Downloading nvidia_cudnn_cu12-9.1.0.70-py3-none-manylinux2014_x86_64.whl.metadata (1.6 kB)\n", "Collecting nvidia-cublas-cu12==12.4.5.8 (from torch>=1.8.0->ultralytics)\n", "  Downloading nvidia_cublas_cu12-12.4.5.8-py3-none-manylinux2014_x86_64.whl.metadata (1.5 kB)\n", "Collecting nvidia-cufft-cu12==11.2.1.3 (from torch>=1.8.0->ultralytics)\n", "  Downloading nvidia_cufft_cu12-11.2.1.3-py3-none-manylinux2014_x86_64.whl.metadata (1.5 kB)\n", "Collecting nvidia-curand-cu12==10.3.5.147 (from torch>=1.8.0->ultralytics)\n", "  Downloading nvidia_curand_cu12-10.3.5.147-py3-none-manylinux2014_x86_64.whl.metadata (1.5 kB)\n", "Collecting nvidia-cusolver-cu12==11.6.1.9 (from torch>=1.8.0->ultralytics)\n", "  Downloading nvidia_cusolver_cu12-11.6.1.9-py3-none-manylinux2014_x86_64.whl.metadata (1.6 kB)\n", "Collecting nvidia-cusparse-cu12==12.3.1.170 (from torch>=1.8.0->ultralytics)\n", "  Downloading nvidia_cusparse_cu12-12.3.1.170-py3-none-manylinux2014_x86_64.whl.metadata (1.6 kB)\n", "Requirement already satisfied: nvidia-cusparselt-cu12==0.6.2 in /usr/local/lib/python3.11/dist-packages (from torch>=1.8.0->ultralytics) (0.6.2)\n", "Requirement already satisfied: nvidia-nccl-cu12==2.21.5 in /usr/local/lib/python3.11/dist-packages (from torch>=1.8.0->ultralytics) (2.21.5)\n", "Requirement already satisfied: nvidia-nvtx-cu12==12.4.127 in /usr/local/lib/python3.11/dist-packages (from torch>=1.8.0->ultralytics) (12.4.127)\n", "Collecting nvidia-nvjitlink-cu12==12.4.127 (from torch>=1.8.0->ultralytics)\n", "  Downloading nvidia_nvjitlink_cu12-12.4.127-py3-none-manylinux2014_x86_64.whl.metadata (1.5 kB)\n", "Requirement already satisfied: triton==3.2.0 in /usr/local/lib/python3.11/dist-packages (from torch>=1.8.0->ultralytics) (3.2.0)\n", "Requirement already satisfied: sympy==1.13.1 in /usr/local/lib/python3.11/dist-packages (from torch>=1.8.0->ultralytics) (1.13.1)\n", "Requirement already satisfied: mpmath<1.4,>=1.1.0 in /usr/local/lib/python3.11/dist-packages (from sympy==1.13.1->torch>=1.8.0->ultralytics) (1.3.0)\n", "Requirement already satisfied: six>=1.5 in /usr/local/lib/python3.11/dist-packages (from python-dateutil>=2.7->matplotlib>=3.3.0->ultralytics) (1.17.0)\n", "Requirement already satisfied: MarkupSafe>=2.0 in /usr/local/lib/python3.11/dist-packages (from jinja2->torch>=1.8.0->ultralytics) (3.0.2)\n", "Downloading ultralytics-8.3.159-py3-none-any.whl (1.0 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m1.0/1.0 MB\u001b[0m \u001b[31m36.0 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading nvidia_cublas_cu12-12.4.5.8-py3-none-manylinux2014_x86_64.whl (363.4 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m363.4/363.4 MB\u001b[0m \u001b[31m4.1 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading nvidia_cuda_cupti_cu12-12.4.127-py3-none-manylinux2014_x86_64.whl (13.8 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m13.8/13.8 MB\u001b[0m \u001b[31m71.6 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading nvidia_cuda_nvrtc_cu12-12.4.127-py3-none-manylinux2014_x86_64.whl (24.6 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m24.6/24.6 MB\u001b[0m \u001b[31m33.1 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading nvidia_cuda_runtime_cu12-12.4.127-py3-none-manylinux2014_x86_64.whl (883 kB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m883.7/883.7 kB\u001b[0m \u001b[31m45.9 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading nvidia_cudnn_cu12-9.1.0.70-py3-none-manylinux2014_x86_64.whl (664.8 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m664.8/664.8 MB\u001b[0m \u001b[31m1.2 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading nvidia_cufft_cu12-11.2.1.3-py3-none-manylinux2014_x86_64.whl (211.5 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m211.5/211.5 MB\u001b[0m \u001b[31m6.3 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading nvidia_curand_cu12-10.3.5.147-py3-none-manylinux2014_x86_64.whl (56.3 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m56.3/56.3 MB\u001b[0m \u001b[31m13.4 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading nvidia_cusolver_cu12-11.6.1.9-py3-none-manylinux2014_x86_64.whl (127.9 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m127.9/127.9 MB\u001b[0m \u001b[31m7.4 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading nvidia_cusparse_cu12-12.3.1.170-py3-none-manylinux2014_x86_64.whl (207.5 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m207.5/207.5 MB\u001b[0m \u001b[31m5.7 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading nvidia_nvjitlink_cu12-12.4.127-py3-none-manylinux2014_x86_64.whl (21.1 MB)\n", "\u001b[2K   \u001b[90m━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━━\u001b[0m \u001b[32m21.1/21.1 MB\u001b[0m \u001b[31m43.9 MB/s\u001b[0m eta \u001b[36m0:00:00\u001b[0m\n", "\u001b[?25hDownloading ultralytics_thop-2.0.14-py3-none-any.whl (26 kB)\n", "Installing collected packages: nvidia-nvjitlink-cu12, nvidia-curand-cu12, nvidia-cufft-cu12, nvidia-cuda-runtime-cu12, nvidia-cuda-nvrtc-cu12, nvidia-cuda-cupti-cu12, nvidia-cublas-cu12, nvidia-cusparse-cu12, nvidia-cudnn-cu12, nvidia-cusolver-cu12, ultralytics-thop, ultralytics\n", "  Attempting uninstall: nvidia-nvjitlink-cu12\n", "    Found existing installation: nvidia-nvjitlink-cu12 12.5.82\n", "    Uninstalling nvidia-nvjitlink-cu12-12.5.82:\n", "      Successfully uninstalled nvidia-nvjitlink-cu12-12.5.82\n", "  Attempting uninstall: nvidia-curand-cu12\n", "    Found existing installation: nvidia-curand-cu12 10.3.6.82\n", "    Uninstalling nvidia-curand-cu12-10.3.6.82:\n", "      Successfully uninstalled nvidia-curand-cu12-10.3.6.82\n", "  Attempting uninstall: nvidia-cufft-cu12\n", "    Found existing installation: nvidia-cufft-cu12 11.2.3.61\n", "    Uninstalling nvidia-cufft-cu12-11.2.3.61:\n", "      Successfully uninstalled nvidia-cufft-cu12-11.2.3.61\n", "  Attempting uninstall: nvidia-cuda-runtime-cu12\n", "    Found existing installation: nvidia-cuda-runtime-cu12 12.5.82\n", "    Uninstalling nvidia-cuda-runtime-cu12-12.5.82:\n", "      Successfully uninstalled nvidia-cuda-runtime-cu12-12.5.82\n", "  Attempting uninstall: nvidia-cuda-nvrtc-cu12\n", "    Found existing installation: nvidia-cuda-nvrtc-cu12 12.5.82\n", "    Uninstalling nvidia-cuda-nvrtc-cu12-12.5.82:\n", "      Successfully uninstalled nvidia-cuda-nvrtc-cu12-12.5.82\n", "  Attempting uninstall: nvidia-cuda-cupti-cu12\n", "    Found existing installation: nvidia-cuda-cupti-cu12 12.5.82\n", "    Uninstalling nvidia-cuda-cupti-cu12-12.5.82:\n", "      Successfully uninstalled nvidia-cuda-cupti-cu12-12.5.82\n", "  Attempting uninstall: nvidia-cublas-cu12\n", "    Found existing installation: nvidia-cublas-cu12 12.5.3.2\n", "    Uninstalling nvidia-cublas-cu12-12.5.3.2:\n", "      Successfully uninstalled nvidia-cublas-cu12-12.5.3.2\n", "  Attempting uninstall: nvidia-cusparse-cu12\n", "    Found existing installation: nvidia-cusparse-cu12 12.5.1.3\n", "    Uninstalling nvidia-cusparse-cu12-12.5.1.3:\n", "      Successfully uninstalled nvidia-cusparse-cu12-12.5.1.3\n", "  Attempting uninstall: nvidia-cudnn-cu12\n", "    Found existing installation: nvidia-cudnn-cu12 9.3.0.75\n", "    Uninstalling nvidia-cudnn-cu12-9.3.0.75:\n", "      Successfully uninstalled nvidia-cudnn-cu12-9.3.0.75\n", "  Attempting uninstall: nvidia-cusolver-cu12\n", "    Found existing installation: nvidia-cusolver-cu12 11.6.3.83\n", "    Uninstalling nvidia-cusolver-cu12-11.6.3.83:\n", "      Successfully uninstalled nvidia-cusolver-cu12-11.6.3.83\n", "Successfully installed nvidia-cublas-cu12-12.4.5.8 nvidia-cuda-cupti-cu12-12.4.127 nvidia-cuda-nvrtc-cu12-12.4.127 nvidia-cuda-runtime-cu12-12.4.127 nvidia-cudnn-cu12-9.1.0.70 nvidia-cufft-cu12-11.2.1.3 nvidia-curand-cu12-10.3.5.147 nvidia-cusolver-cu12-11.6.1.9 nvidia-cusparse-cu12-12.3.1.170 nvidia-nvjitlink-cu12-12.4.127 ultralytics-8.3.159 ultralytics-thop-2.0.14\n"]}]}, {"cell_type": "markdown", "source": ["### Downloading the dataset from Roboflow"], "metadata": {"id": "HxjSLRwZ0p8B"}}, {"cell_type": "code", "source": ["from roboflow import Roboflow\n", "rf = Roboflow(api_key=\"YOUR API KEY\")\n", "project = rf.workspace(\"YOUR WORKSPACE\").project(\"DATASET NAME\")\n", "version = project.version(1)\n", "dataset = version.download(\"yolov8\")"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "4usenzfy0dLE", "outputId": "860a7798-63c5-4362-e00c-dae63598040a"}, "execution_count": 2, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["loading Roboflow workspace...\n", "loading Roboflow project...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["Downloading Dataset Version Zip in PlantDoc-1 to yolov8:: 100%|██████████| 449261/449261 [00:13<00:00, 32524.67it/s]"]}, {"output_type": "stream", "name": "stdout", "text": ["\n"]}, {"output_type": "stream", "name": "stderr", "text": ["\n", "Extracting Dataset Version Zip to PlantDoc-1 in yolov8:: 100%|██████████| 5150/5150 [00:01<00:00, 3132.62it/s]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["Creating new Ultralytics Settings v0.0.6 file ✅ \n", "View Ultralytics Settings with 'yolo settings' or at '/root/.config/Ultralytics/settings.json'\n", "Update Settings with 'yolo settings key=value', i.e. 'yolo settings runs_dir=path/to/dir'. For help see https://docs.ultralytics.com/quickstart/#ultralytics-settings.\n"]}]}, {"cell_type": "markdown", "source": ["###Train the YOLOV8 MODEL"], "metadata": {"id": "rTTs-QVE1qwx"}}, {"cell_type": "code", "source": ["from ultralytics import YOLO\n", "\n", "model = YOLO(\"yolov8n.pt\")\n", "results = model.train(data=\"/content/PlantDoc-1/data.yaml\", epochs=100, batch = 16)"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "J54RDpxz0dJA", "outputId": "e131aff3-d764-43eb-beb9-017c4523db37"}, "execution_count": 4, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["Ultralytics 8.3.159 🚀 Python-3.11.13 torch-2.6.0+cu124 CUDA:0 (Tesla T4, 15095MiB)\n", "\u001b[34m\u001b[1mengine/trainer: \u001b[0magnostic_nms=False, amp=True, augment=False, auto_augment=randaugment, batch=16, bgr=0.0, box=7.5, cache=False, cfg=None, classes=None, close_mosaic=10, cls=0.5, conf=None, copy_paste=0.0, copy_paste_mode=flip, cos_lr=False, cutmix=0.0, data=/content/PlantDoc-1/data.yaml, degrees=0.0, deterministic=True, device=None, dfl=1.5, dnn=False, dropout=0.0, dynamic=False, embed=None, epochs=100, erasing=0.4, exist_ok=False, fliplr=0.5, flipud=0.0, format=torchscript, fraction=1.0, freeze=None, half=False, hsv_h=0.015, hsv_s=0.7, hsv_v=0.4, imgsz=640, int8=False, iou=0.7, keras=False, kobj=1.0, line_width=None, lr0=0.01, lrf=0.01, mask_ratio=4, max_det=300, mixup=0.0, mode=train, model=yolov8n.pt, momentum=0.937, mosaic=1.0, multi_scale=False, name=train2, nbs=64, nms=False, opset=None, optimize=False, optimizer=auto, overlap_mask=True, patience=100, perspective=0.0, plots=True, pose=12.0, pretrained=True, profile=False, project=None, rect=False, resume=False, retina_masks=False, save=True, save_conf=False, save_crop=False, save_dir=runs/detect/train2, save_frames=False, save_json=False, save_period=-1, save_txt=False, scale=0.5, seed=0, shear=0.0, show=False, show_boxes=True, show_conf=True, show_labels=True, simplify=True, single_cls=False, source=None, split=val, stream_buffer=False, task=detect, time=None, tracker=botsort.yaml, translate=0.1, val=True, verbose=True, vid_stride=1, visualize=False, warmup_bias_lr=0.1, warmup_epochs=3.0, warmup_momentum=0.8, weight_decay=0.0005, workers=8, workspace=None\n", "Downloading https://ultralytics.com/assets/Arial.ttf to '/root/.config/Ultralytics/Arial.ttf'...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["100%|██████████| 755k/755k [00:00<00:00, 41.8MB/s]"]}, {"output_type": "stream", "name": "stdout", "text": ["Overriding model.yaml nc=80 with nc=30\n", "\n", "                   from  n    params  module                                       arguments                     \n", "  0                  -1  1       464  ultralytics.nn.modules.conv.Conv             [3, 16, 3, 2]                 \n", "  1                  -1  1      4672  ultralytics.nn.modules.conv.Conv             [16, 32, 3, 2]                \n", "  2                  -1  1      7360  ultralytics.nn.modules.block.C2f             [32, 32, 1, True]             \n", "  3                  -1  1     18560  ultralytics.nn.modules.conv.Conv             [32, 64, 3, 2]                \n", "  4                  -1  2     49664  ultralytics.nn.modules.block.C2f             [64, 64, 2, True]             \n", "  5                  -1  1     73984  ultralytics.nn.modules.conv.Conv             [64, 128, 3, 2]               \n", "  6                  -1  2    197632  ultralytics.nn.modules.block.C2f             [128, 128, 2, True]           \n", "  7                  -1  1    295424  ultralytics.nn.modules.conv.Conv             [128, 256, 3, 2]              \n", "  8                  -1  1    460288  ultralytics.nn.modules.block.C2f             [256, 256, 1, True]           \n", "  9                  -1  1    164608  ultralytics.nn.modules.block.SPPF            [256, 256, 5]                 \n", " 10                  -1  1         0  torch.nn.modules.upsampling.Upsample         [None, 2, 'nearest']          \n", " 11             [-1, 6]  1         0  ultralytics.nn.modules.conv.Concat           [1]                           \n", " 12                  -1  1    148224  ultralytics.nn.modules.block.C2f             [384, 128, 1]                 \n", " 13                  -1  1         0  torch.nn.modules.upsampling.Upsample         [None, 2, 'nearest']          \n", " 14             [-1, 4]  1         0  ultralytics.nn.modules.conv.Concat           [1]                           \n", " 15                  -1  1     37248  ultralytics.nn.modules.block.C2f             [192, 64, 1]                  \n", " 16                  -1  1     36992  ultralytics.nn.modules.conv.Conv             [64, 64, 3, 2]                \n", " 17            [-1, 12]  1         0  ultralytics.nn.modules.conv.Concat           [1]                           \n", " 18                  -1  1    123648  ultralytics.nn.modules.block.C2f             [192, 128, 1]                 \n", " 19                  -1  1    147712  ultralytics.nn.modules.conv.Conv             [128, 128, 3, 2]              \n", " 20             [-1, 9]  1         0  ultralytics.nn.modules.conv.Concat           [1]                           \n", " 21                  -1  1    493056  ultralytics.nn.modules.block.C2f             [384, 256, 1]                 \n", " 22        [15, 18, 21]  1    757162  ultralytics.nn.modules.head.Detect           [30, [64, 128, 256]]          \n"]}, {"output_type": "stream", "name": "stderr", "text": ["\n"]}, {"output_type": "stream", "name": "stdout", "text": ["Model summary: 129 layers, 3,016,698 parameters, 3,016,682 gradients, 8.2 GFLOPs\n", "\n", "Transferred 319/355 items from pretrained weights\n", "Freezing layer 'model.22.dfl.conv.weight'\n", "\u001b[34m\u001b[1mAMP: \u001b[0mrunning Automatic Mixed Precision (AMP) checks...\n", "Downloading https://github.com/ultralytics/assets/releases/download/v8.3.0/yolo11n.pt to 'yolo11n.pt'...\n"]}, {"output_type": "stream", "name": "stderr", "text": ["100%|██████████| 5.35M/5.35M [00:00<00:00, 172MB/s]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["\u001b[34m\u001b[1mAMP: \u001b[0mchecks passed ✅\n", "\u001b[34m\u001b[1mtrain: \u001b[0mFast image access ✅ (ping: 0.0±0.0 ms, read: 1227.0±524.8 MB/s, size: 43.0 KB)\n"]}, {"output_type": "stream", "name": "stderr", "text": ["\u001b[34m\u001b[1mtrain: \u001b[0mScanning /content/PlantDoc-1/train/labels... 2009 images, 9 backgrounds, 0 corrupt: 100%|██████████| 2009/2009 [00:00<00:00, 2394.19it/s]"]}, {"output_type": "stream", "name": "stdout", "text": ["\u001b[34m\u001b[1mtrain: \u001b[0mNew cache created: /content/PlantDoc-1/train/labels.cache\n"]}, {"output_type": "stream", "name": "stderr", "text": ["\n"]}, {"output_type": "stream", "name": "stdout", "text": ["\u001b[34m\u001b[1malbumentations: \u001b[0mB<PERSON>r(p=0.01, blur_limit=(3, 7)), MedianBlur(p=0.01, blur_limit=(3, 7)), ToGray(p=0.01, method='weighted_average', num_output_channels=3), CLAHE(p=0.01, clip_limit=(1.0, 4.0), tile_grid_size=(8, 8))\n", "\u001b[34m\u001b[1mval: \u001b[0mFast image access ✅ (ping: 0.0±0.0 ms, read: 712.5±270.1 MB/s, size: 259.3 KB)\n"]}, {"output_type": "stream", "name": "stderr", "text": ["\u001b[34m\u001b[1mval: \u001b[0mScanning /content/PlantDoc-1/valid/labels... 314 images, 1 backgrounds, 0 corrupt: 100%|██████████| 314/314 [00:00<00:00, 1214.19it/s]"]}, {"output_type": "stream", "name": "stdout", "text": ["\u001b[34m\u001b[1mval: \u001b[0mNew cache created: /content/PlantDoc-1/valid/labels.cache\n"]}, {"output_type": "stream", "name": "stderr", "text": ["\n"]}, {"output_type": "stream", "name": "stdout", "text": ["Plotting labels to runs/detect/train2/labels.jpg... \n", "\u001b[34m\u001b[1moptimizer:\u001b[0m 'optimizer=auto' found, ignoring 'lr0=0.01' and 'momentum=0.937' and determining best 'optimizer', 'lr0' and 'momentum' automatically... \n", "\u001b[34m\u001b[1moptimizer:\u001b[0m AdamW(lr=0.000294, momentum=0.9) with parameter groups 57 weight(decay=0.0), 64 weight(decay=0.0005), 63 bias(decay=0.0)\n", "Image sizes 640 train, 640 val\n", "Using 2 dataloader workers\n", "Logging results to \u001b[1mruns/detect/train2\u001b[0m\n", "Starting training for 100 epochs...\n", "\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"output_type": "stream", "name": "stderr", "text": ["      1/100      2.27G      1.268      4.299       1.45         49        640: 100%|██████████| 126/126 [00:49<00:00,  2.57it/s]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 10/10 [00:04<00:00,  2.05it/s]"]}, {"output_type": "stream", "name": "stdout", "text": ["                   all        314       1191     0.0305      0.674     0.0547     0.0369\n"]}, {"output_type": "stream", "name": "stderr", "text": ["\n"]}, {"output_type": "stream", "name": "stdout", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"output_type": "stream", "name": "stderr", "text": ["      2/100      2.86G      1.275      3.591      1.429         80        640: 100%|██████████| 126/126 [00:46<00:00,  2.72it/s]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 10/10 [00:04<00:00,  2.39it/s]"]}, {"output_type": "stream", "name": "stdout", "text": ["                   all        314       1191      0.204      0.245      0.169      0.115\n"]}, {"output_type": "stream", "name": "stderr", "text": ["\n"]}, {"output_type": "stream", "name": "stdout", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"output_type": "stream", "name": "stderr", "text": ["      3/100      2.88G      1.312      3.144      1.449         87        640: 100%|██████████| 126/126 [00:46<00:00,  2.71it/s]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 10/10 [00:03<00:00,  2.88it/s]"]}, {"output_type": "stream", "name": "stdout", "text": ["                   all        314       1191      0.202      0.439      0.187      0.129\n"]}, {"output_type": "stream", "name": "stderr", "text": ["\n"]}, {"output_type": "stream", "name": "stdout", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"output_type": "stream", "name": "stderr", "text": ["      4/100      2.89G      1.292      2.924      1.436         54        640: 100%|██████████| 126/126 [00:46<00:00,  2.72it/s]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 10/10 [00:03<00:00,  2.73it/s]"]}, {"output_type": "stream", "name": "stdout", "text": ["                   all        314       1191      0.243      0.422      0.241      0.158\n"]}, {"output_type": "stream", "name": "stderr", "text": ["\n"]}, {"output_type": "stream", "name": "stdout", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"output_type": "stream", "name": "stderr", "text": ["      5/100      2.91G       1.28      2.707      1.416         57        640: 100%|██████████| 126/126 [00:46<00:00,  2.71it/s]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 10/10 [00:03<00:00,  2.52it/s]"]}, {"output_type": "stream", "name": "stdout", "text": ["                   all        314       1191      0.242      0.455      0.272      0.182\n"]}, {"output_type": "stream", "name": "stderr", "text": ["\n"]}, {"output_type": "stream", "name": "stdout", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"output_type": "stream", "name": "stderr", "text": ["      6/100      2.92G      1.257      2.604      1.406         44        640: 100%|██████████| 126/126 [00:45<00:00,  2.79it/s]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 10/10 [00:04<00:00,  2.31it/s]"]}, {"output_type": "stream", "name": "stdout", "text": ["                   all        314       1191      0.273      0.483      0.337      0.222\n"]}, {"output_type": "stream", "name": "stderr", "text": ["\n"]}, {"output_type": "stream", "name": "stdout", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"output_type": "stream", "name": "stderr", "text": ["      7/100      2.95G      1.233      2.486      1.388         51        640: 100%|██████████| 126/126 [00:45<00:00,  2.80it/s]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 10/10 [00:04<00:00,  2.01it/s]"]}, {"output_type": "stream", "name": "stdout", "text": ["                   all        314       1191      0.276      0.427      0.302      0.204\n"]}, {"output_type": "stream", "name": "stderr", "text": ["\n"]}, {"output_type": "stream", "name": "stdout", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"output_type": "stream", "name": "stderr", "text": ["      8/100      2.96G       1.22      2.386       1.37         56        640: 100%|██████████| 126/126 [00:44<00:00,  2.84it/s]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 10/10 [00:04<00:00,  2.23it/s]"]}, {"output_type": "stream", "name": "stdout", "text": ["                   all        314       1191      0.347      0.504      0.375       0.25\n"]}, {"output_type": "stream", "name": "stderr", "text": ["\n"]}, {"output_type": "stream", "name": "stdout", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"output_type": "stream", "name": "stderr", "text": ["      9/100      2.98G      1.206      2.285      1.361         42        640: 100%|██████████| 126/126 [00:43<00:00,  2.87it/s]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 10/10 [00:03<00:00,  2.92it/s]"]}, {"output_type": "stream", "name": "stdout", "text": ["                   all        314       1191      0.461      0.373      0.394      0.276\n"]}, {"output_type": "stream", "name": "stderr", "text": ["\n"]}, {"output_type": "stream", "name": "stdout", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"output_type": "stream", "name": "stderr", "text": ["     10/100      2.99G      1.208      2.207      1.349         76        640: 100%|██████████| 126/126 [00:45<00:00,  2.74it/s]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 10/10 [00:03<00:00,  2.73it/s]"]}, {"output_type": "stream", "name": "stdout", "text": ["                   all        314       1191      0.367      0.471      0.402      0.279\n"]}, {"output_type": "stream", "name": "stderr", "text": ["\n"]}, {"output_type": "stream", "name": "stdout", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"output_type": "stream", "name": "stderr", "text": ["     11/100      3.02G      1.184      2.141      1.341         81        640: 100%|██████████| 126/126 [00:44<00:00,  2.81it/s]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 10/10 [00:03<00:00,  2.59it/s]"]}, {"output_type": "stream", "name": "stdout", "text": ["                   all        314       1191      0.336      0.526      0.403      0.282\n"]}, {"output_type": "stream", "name": "stderr", "text": ["\n"]}, {"output_type": "stream", "name": "stdout", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"output_type": "stream", "name": "stderr", "text": ["     12/100      3.03G      1.198      2.118      1.345         44        640: 100%|██████████| 126/126 [00:44<00:00,  2.82it/s]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 10/10 [00:04<00:00,  2.23it/s]"]}, {"output_type": "stream", "name": "stdout", "text": ["                   all        314       1191      0.345      0.452      0.378      0.253\n"]}, {"output_type": "stream", "name": "stderr", "text": ["\n"]}, {"output_type": "stream", "name": "stdout", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"output_type": "stream", "name": "stderr", "text": ["     13/100      3.05G      1.193      2.069      1.337         88        640: 100%|██████████| 126/126 [00:44<00:00,  2.86it/s]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 10/10 [00:03<00:00,  2.69it/s]"]}, {"output_type": "stream", "name": "stdout", "text": ["                   all        314       1191       0.44      0.482      0.431      0.294\n"]}, {"output_type": "stream", "name": "stderr", "text": ["\n"]}, {"output_type": "stream", "name": "stdout", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"output_type": "stream", "name": "stderr", "text": ["     14/100      3.06G      1.196      2.021      1.336         71        640: 100%|██████████| 126/126 [00:45<00:00,  2.79it/s]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 10/10 [00:03<00:00,  2.64it/s]"]}, {"output_type": "stream", "name": "stdout", "text": ["                   all        314       1191      0.375      0.457      0.455      0.315\n"]}, {"output_type": "stream", "name": "stderr", "text": ["\n"]}, {"output_type": "stream", "name": "stdout", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"output_type": "stream", "name": "stderr", "text": ["     15/100      3.09G      1.172       1.95      1.318         54        640: 100%|██████████| 126/126 [00:45<00:00,  2.77it/s]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 10/10 [00:03<00:00,  2.76it/s]"]}, {"output_type": "stream", "name": "stdout", "text": ["                   all        314       1191      0.344      0.582       0.41      0.284\n"]}, {"output_type": "stream", "name": "stderr", "text": ["\n"]}, {"output_type": "stream", "name": "stdout", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"output_type": "stream", "name": "stderr", "text": ["     16/100      3.09G      1.172      1.943      1.327         79        640: 100%|██████████| 126/126 [00:44<00:00,  2.86it/s]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 10/10 [00:05<00:00,  1.93it/s]"]}, {"output_type": "stream", "name": "stdout", "text": ["                   all        314       1191      0.389      0.486      0.455      0.318\n"]}, {"output_type": "stream", "name": "stderr", "text": ["\n"]}, {"output_type": "stream", "name": "stdout", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"output_type": "stream", "name": "stderr", "text": ["     17/100      3.12G      1.166      1.925      1.324         49        640: 100%|██████████| 126/126 [00:44<00:00,  2.80it/s]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 10/10 [00:04<00:00,  2.06it/s]"]}, {"output_type": "stream", "name": "stdout", "text": ["                   all        314       1191      0.359      0.569      0.472      0.329\n"]}, {"output_type": "stream", "name": "stderr", "text": ["\n"]}, {"output_type": "stream", "name": "stdout", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"output_type": "stream", "name": "stderr", "text": ["     18/100      3.13G      1.162      1.845      1.308         49        640: 100%|██████████| 126/126 [00:44<00:00,  2.82it/s]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 10/10 [00:03<00:00,  2.89it/s]"]}, {"output_type": "stream", "name": "stdout", "text": ["                   all        314       1191        0.4      0.457      0.451      0.314\n"]}, {"output_type": "stream", "name": "stderr", "text": ["\n"]}, {"output_type": "stream", "name": "stdout", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"output_type": "stream", "name": "stderr", "text": ["     19/100      3.15G      1.139      1.813      1.307         65        640: 100%|██████████| 126/126 [00:46<00:00,  2.73it/s]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 10/10 [00:03<00:00,  2.85it/s]"]}, {"output_type": "stream", "name": "stdout", "text": ["                   all        314       1191      0.446      0.519      0.479      0.339\n"]}, {"output_type": "stream", "name": "stderr", "text": ["\n"]}, {"output_type": "stream", "name": "stdout", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"output_type": "stream", "name": "stderr", "text": ["     20/100      3.16G      1.155      1.771      1.297         50        640: 100%|██████████| 126/126 [00:47<00:00,  2.68it/s]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 10/10 [00:03<00:00,  2.83it/s]"]}, {"output_type": "stream", "name": "stdout", "text": ["                   all        314       1191      0.423      0.517      0.453      0.308\n"]}, {"output_type": "stream", "name": "stderr", "text": ["\n"]}, {"output_type": "stream", "name": "stdout", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"output_type": "stream", "name": "stderr", "text": ["     21/100      3.19G      1.133      1.762      1.299         44        640: 100%|██████████| 126/126 [00:46<00:00,  2.70it/s]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 10/10 [00:03<00:00,  2.67it/s]"]}, {"output_type": "stream", "name": "stdout", "text": ["                   all        314       1191      0.382      0.493       0.45      0.308\n"]}, {"output_type": "stream", "name": "stderr", "text": ["\n"]}, {"output_type": "stream", "name": "stdout", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"output_type": "stream", "name": "stderr", "text": ["     22/100       3.2G      1.138       1.72      1.294         81        640: 100%|██████████| 126/126 [00:45<00:00,  2.78it/s]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 10/10 [00:04<00:00,  2.14it/s]"]}, {"output_type": "stream", "name": "stdout", "text": ["                   all        314       1191      0.447      0.476      0.506      0.358\n"]}, {"output_type": "stream", "name": "stderr", "text": ["\n"]}, {"output_type": "stream", "name": "stdout", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"output_type": "stream", "name": "stderr", "text": ["     23/100       3.4G      1.144      1.719      1.299         47        640: 100%|██████████| 126/126 [00:45<00:00,  2.77it/s]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 10/10 [00:04<00:00,  2.05it/s]"]}, {"output_type": "stream", "name": "stdout", "text": ["                   all        314       1191      0.513      0.483      0.499       0.34\n"]}, {"output_type": "stream", "name": "stderr", "text": ["\n"]}, {"output_type": "stream", "name": "stdout", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"output_type": "stream", "name": "stderr", "text": ["     24/100      3.41G      1.139      1.687      1.292         59        640: 100%|██████████| 126/126 [00:44<00:00,  2.81it/s]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 10/10 [00:04<00:00,  2.49it/s]"]}, {"output_type": "stream", "name": "stdout", "text": ["                   all        314       1191      0.408      0.587      0.508      0.349\n"]}, {"output_type": "stream", "name": "stderr", "text": ["\n"]}, {"output_type": "stream", "name": "stdout", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"output_type": "stream", "name": "stderr", "text": ["     25/100      3.43G      1.121      1.639       1.27         53        640: 100%|██████████| 126/126 [00:45<00:00,  2.76it/s]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 10/10 [00:03<00:00,  2.68it/s]"]}, {"output_type": "stream", "name": "stdout", "text": ["                   all        314       1191      0.456      0.478       0.47       0.33\n"]}, {"output_type": "stream", "name": "stderr", "text": ["\n"]}, {"output_type": "stream", "name": "stdout", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"output_type": "stream", "name": "stderr", "text": ["     26/100      3.44G      1.126      1.639      1.279         33        640: 100%|██████████| 126/126 [00:46<00:00,  2.74it/s]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 10/10 [00:03<00:00,  2.82it/s]"]}, {"output_type": "stream", "name": "stdout", "text": ["                   all        314       1191      0.333      0.578      0.487      0.343\n"]}, {"output_type": "stream", "name": "stderr", "text": ["\n"]}, {"output_type": "stream", "name": "stdout", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"output_type": "stream", "name": "stderr", "text": ["     27/100      3.47G      1.124      1.615      1.278         41        640: 100%|██████████| 126/126 [00:45<00:00,  2.76it/s]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 10/10 [00:03<00:00,  2.66it/s]"]}, {"output_type": "stream", "name": "stdout", "text": ["                   all        314       1191      0.372      0.518      0.476      0.325\n"]}, {"output_type": "stream", "name": "stderr", "text": ["\n"]}, {"output_type": "stream", "name": "stdout", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"output_type": "stream", "name": "stderr", "text": ["     28/100      3.48G      1.101      1.568      1.267         64        640: 100%|██████████| 126/126 [00:44<00:00,  2.82it/s]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 10/10 [00:04<00:00,  2.19it/s]"]}, {"output_type": "stream", "name": "stdout", "text": ["                   all        314       1191      0.521      0.515      0.542      0.382\n"]}, {"output_type": "stream", "name": "stderr", "text": ["\n"]}, {"output_type": "stream", "name": "stdout", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"output_type": "stream", "name": "stderr", "text": ["     29/100       3.5G      1.107      1.558      1.259         51        640: 100%|██████████| 126/126 [00:44<00:00,  2.83it/s]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 10/10 [00:04<00:00,  2.24it/s]"]}, {"output_type": "stream", "name": "stdout", "text": ["                   all        314       1191      0.455       0.57      0.507      0.362\n"]}, {"output_type": "stream", "name": "stderr", "text": ["\n"]}, {"output_type": "stream", "name": "stdout", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"output_type": "stream", "name": "stderr", "text": ["     30/100      3.51G      1.091      1.533      1.259         53        640: 100%|██████████| 126/126 [00:44<00:00,  2.84it/s]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 10/10 [00:03<00:00,  2.68it/s]"]}, {"output_type": "stream", "name": "stdout", "text": ["                   all        314       1191      0.447      0.556      0.494      0.346\n"]}, {"output_type": "stream", "name": "stderr", "text": ["\n"]}, {"output_type": "stream", "name": "stdout", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"output_type": "stream", "name": "stderr", "text": ["     31/100      3.54G      1.111      1.529      1.266         77        640: 100%|██████████| 126/126 [00:46<00:00,  2.73it/s]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 10/10 [00:03<00:00,  2.73it/s]"]}, {"output_type": "stream", "name": "stdout", "text": ["                   all        314       1191      0.416       0.53      0.495      0.351\n"]}, {"output_type": "stream", "name": "stderr", "text": ["\n"]}, {"output_type": "stream", "name": "stdout", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"output_type": "stream", "name": "stderr", "text": ["     32/100      3.54G      1.091      1.536      1.266         77        640: 100%|██████████| 126/126 [00:45<00:00,  2.75it/s]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 10/10 [00:03<00:00,  2.77it/s]"]}, {"output_type": "stream", "name": "stdout", "text": ["                   all        314       1191      0.439      0.587      0.501      0.346\n"]}, {"output_type": "stream", "name": "stderr", "text": ["\n"]}, {"output_type": "stream", "name": "stdout", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"output_type": "stream", "name": "stderr", "text": ["     33/100      3.57G      1.099      1.486      1.257         50        640: 100%|██████████| 126/126 [00:44<00:00,  2.83it/s]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 10/10 [00:04<00:00,  2.25it/s]"]}, {"output_type": "stream", "name": "stdout", "text": ["                   all        314       1191      0.436      0.538      0.499      0.357\n"]}, {"output_type": "stream", "name": "stderr", "text": ["\n"]}, {"output_type": "stream", "name": "stdout", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"output_type": "stream", "name": "stderr", "text": ["     34/100      3.58G      1.088      1.455      1.248         63        640: 100%|██████████| 126/126 [00:44<00:00,  2.84it/s]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 10/10 [00:04<00:00,  2.44it/s]"]}, {"output_type": "stream", "name": "stdout", "text": ["                   all        314       1191      0.443      0.548      0.533      0.374\n"]}, {"output_type": "stream", "name": "stderr", "text": ["\n"]}, {"output_type": "stream", "name": "stdout", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"output_type": "stream", "name": "stderr", "text": ["     35/100       3.6G      1.077      1.438       1.25        101        640: 100%|██████████| 126/126 [00:44<00:00,  2.84it/s]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 10/10 [00:03<00:00,  2.82it/s]"]}, {"output_type": "stream", "name": "stdout", "text": ["                   all        314       1191      0.507      0.601      0.551      0.398\n"]}, {"output_type": "stream", "name": "stderr", "text": ["\n"]}, {"output_type": "stream", "name": "stdout", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"output_type": "stream", "name": "stderr", "text": ["     36/100      3.61G      1.091      1.416      1.244         46        640: 100%|██████████| 126/126 [00:45<00:00,  2.77it/s]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 10/10 [00:03<00:00,  2.96it/s]"]}, {"output_type": "stream", "name": "stdout", "text": ["                   all        314       1191      0.426      0.601      0.506      0.359\n"]}, {"output_type": "stream", "name": "stderr", "text": ["\n"]}, {"output_type": "stream", "name": "stdout", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"output_type": "stream", "name": "stderr", "text": ["     37/100      3.64G      1.084      1.386      1.242         80        640: 100%|██████████| 126/126 [00:44<00:00,  2.81it/s]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 10/10 [00:04<00:00,  2.26it/s]"]}, {"output_type": "stream", "name": "stdout", "text": ["                   all        314       1191      0.412      0.621      0.525      0.371\n"]}, {"output_type": "stream", "name": "stderr", "text": ["\n"]}, {"output_type": "stream", "name": "stdout", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"output_type": "stream", "name": "stderr", "text": ["     38/100      3.65G      1.079       1.38      1.239         79        640: 100%|██████████| 126/126 [00:44<00:00,  2.80it/s]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 10/10 [00:04<00:00,  2.11it/s]"]}, {"output_type": "stream", "name": "stdout", "text": ["                   all        314       1191      0.522      0.554      0.528      0.372\n"]}, {"output_type": "stream", "name": "stderr", "text": ["\n"]}, {"output_type": "stream", "name": "stdout", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"output_type": "stream", "name": "stderr", "text": ["     39/100      3.67G      1.074      1.368      1.234         73        640: 100%|██████████| 126/126 [00:45<00:00,  2.78it/s]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 10/10 [00:04<00:00,  2.40it/s]"]}, {"output_type": "stream", "name": "stdout", "text": ["                   all        314       1191      0.499      0.582      0.541      0.382\n"]}, {"output_type": "stream", "name": "stderr", "text": ["\n"]}, {"output_type": "stream", "name": "stdout", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"output_type": "stream", "name": "stderr", "text": ["     40/100      3.68G      1.062       1.36       1.24         69        640: 100%|██████████| 126/126 [00:44<00:00,  2.81it/s]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 10/10 [00:03<00:00,  2.74it/s]"]}, {"output_type": "stream", "name": "stdout", "text": ["                   all        314       1191       0.43      0.563      0.498      0.347\n"]}, {"output_type": "stream", "name": "stderr", "text": ["\n"]}, {"output_type": "stream", "name": "stdout", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"output_type": "stream", "name": "stderr", "text": ["     41/100      3.71G      1.067      1.344      1.238         47        640: 100%|██████████| 126/126 [00:46<00:00,  2.73it/s]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 10/10 [00:03<00:00,  2.95it/s]"]}, {"output_type": "stream", "name": "stdout", "text": ["                   all        314       1191      0.471      0.527      0.518      0.361\n"]}, {"output_type": "stream", "name": "stderr", "text": ["\n"]}, {"output_type": "stream", "name": "stdout", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"output_type": "stream", "name": "stderr", "text": ["     42/100      3.72G      1.045      1.337      1.223         65        640: 100%|██████████| 126/126 [00:45<00:00,  2.77it/s]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 10/10 [00:03<00:00,  2.53it/s]"]}, {"output_type": "stream", "name": "stdout", "text": ["                   all        314       1191      0.481      0.569      0.513      0.361\n"]}, {"output_type": "stream", "name": "stderr", "text": ["\n"]}, {"output_type": "stream", "name": "stdout", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"output_type": "stream", "name": "stderr", "text": ["     43/100      3.74G      1.061      1.334      1.233         84        640: 100%|██████████| 126/126 [00:44<00:00,  2.84it/s]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 10/10 [00:04<00:00,  2.18it/s]"]}, {"output_type": "stream", "name": "stdout", "text": ["                   all        314       1191      0.418      0.619      0.514      0.367\n"]}, {"output_type": "stream", "name": "stderr", "text": ["\n"]}, {"output_type": "stream", "name": "stdout", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"output_type": "stream", "name": "stderr", "text": ["     44/100      3.75G      1.054      1.304      1.224         46        640: 100%|██████████| 126/126 [00:44<00:00,  2.84it/s]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 10/10 [00:03<00:00,  2.70it/s]"]}, {"output_type": "stream", "name": "stdout", "text": ["                   all        314       1191      0.448       0.61       0.53      0.376\n"]}, {"output_type": "stream", "name": "stderr", "text": ["\n"]}, {"output_type": "stream", "name": "stdout", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"output_type": "stream", "name": "stderr", "text": ["     45/100      3.78G       1.04      1.273      1.213         49        640: 100%|██████████| 126/126 [00:45<00:00,  2.76it/s]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 10/10 [00:03<00:00,  2.82it/s]"]}, {"output_type": "stream", "name": "stdout", "text": ["                   all        314       1191      0.472      0.509      0.516      0.362\n"]}, {"output_type": "stream", "name": "stderr", "text": ["\n"]}, {"output_type": "stream", "name": "stdout", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"output_type": "stream", "name": "stderr", "text": ["     46/100      3.79G      1.024      1.264      1.211         73        640: 100%|██████████| 126/126 [00:45<00:00,  2.80it/s]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 10/10 [00:03<00:00,  2.74it/s]"]}, {"output_type": "stream", "name": "stdout", "text": ["                   all        314       1191      0.525      0.549      0.553      0.385\n"]}, {"output_type": "stream", "name": "stderr", "text": ["\n"]}, {"output_type": "stream", "name": "stdout", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"output_type": "stream", "name": "stderr", "text": ["     47/100      3.81G      1.055      1.254      1.221         57        640: 100%|██████████| 126/126 [00:45<00:00,  2.80it/s]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 10/10 [00:04<00:00,  2.11it/s]"]}, {"output_type": "stream", "name": "stdout", "text": ["                   all        314       1191       0.52      0.581      0.567      0.399\n"]}, {"output_type": "stream", "name": "stderr", "text": ["\n"]}, {"output_type": "stream", "name": "stdout", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"output_type": "stream", "name": "stderr", "text": ["     48/100      3.82G       1.03      1.261       1.21         71        640: 100%|██████████| 126/126 [00:44<00:00,  2.85it/s]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 10/10 [00:04<00:00,  2.48it/s]"]}, {"output_type": "stream", "name": "stdout", "text": ["                   all        314       1191      0.498      0.516      0.518      0.371\n"]}, {"output_type": "stream", "name": "stderr", "text": ["\n"]}, {"output_type": "stream", "name": "stdout", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"output_type": "stream", "name": "stderr", "text": ["     49/100      3.85G      1.025      1.237      1.212         45        640: 100%|██████████| 126/126 [00:44<00:00,  2.80it/s]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 10/10 [00:03<00:00,  2.71it/s]"]}, {"output_type": "stream", "name": "stdout", "text": ["                   all        314       1191      0.464      0.541      0.532      0.374\n"]}, {"output_type": "stream", "name": "stderr", "text": ["\n"]}, {"output_type": "stream", "name": "stdout", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"output_type": "stream", "name": "stderr", "text": ["     50/100      3.85G      1.026      1.239       1.21         42        640: 100%|██████████| 126/126 [00:45<00:00,  2.75it/s]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 10/10 [00:03<00:00,  2.91it/s]"]}, {"output_type": "stream", "name": "stdout", "text": ["                   all        314       1191      0.406      0.613      0.521      0.364\n"]}, {"output_type": "stream", "name": "stderr", "text": ["\n"]}, {"output_type": "stream", "name": "stdout", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"output_type": "stream", "name": "stderr", "text": ["     51/100      3.88G       1.02      1.221      1.201         52        640: 100%|██████████| 126/126 [00:46<00:00,  2.74it/s]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 10/10 [00:03<00:00,  2.83it/s]"]}, {"output_type": "stream", "name": "stdout", "text": ["                   all        314       1191      0.503      0.448      0.518      0.366\n"]}, {"output_type": "stream", "name": "stderr", "text": ["\n"]}, {"output_type": "stream", "name": "stdout", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"output_type": "stream", "name": "stderr", "text": ["     52/100      3.89G       1.03      1.202      1.208         61        640: 100%|██████████| 126/126 [00:45<00:00,  2.80it/s]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 10/10 [00:04<00:00,  2.17it/s]"]}, {"output_type": "stream", "name": "stdout", "text": ["                   all        314       1191      0.439      0.629      0.511      0.354\n"]}, {"output_type": "stream", "name": "stderr", "text": ["\n"]}, {"output_type": "stream", "name": "stdout", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"output_type": "stream", "name": "stderr", "text": ["     53/100      3.91G       1.01       1.21      1.197         68        640: 100%|██████████| 126/126 [00:45<00:00,  2.76it/s]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 10/10 [00:04<00:00,  2.19it/s]"]}, {"output_type": "stream", "name": "stdout", "text": ["                   all        314       1191      0.451      0.612      0.534      0.373\n"]}, {"output_type": "stream", "name": "stderr", "text": ["\n"]}, {"output_type": "stream", "name": "stdout", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"output_type": "stream", "name": "stderr", "text": ["     54/100      3.92G      1.018      1.186      1.205         67        640: 100%|██████████| 126/126 [00:45<00:00,  2.78it/s]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 10/10 [00:03<00:00,  2.84it/s]"]}, {"output_type": "stream", "name": "stdout", "text": ["                   all        314       1191      0.454      0.566      0.517      0.361\n"]}, {"output_type": "stream", "name": "stderr", "text": ["\n"]}, {"output_type": "stream", "name": "stdout", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"output_type": "stream", "name": "stderr", "text": ["     55/100      3.95G      1.011      1.149      1.196         61        640: 100%|██████████| 126/126 [00:44<00:00,  2.81it/s]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 10/10 [00:03<00:00,  2.83it/s]"]}, {"output_type": "stream", "name": "stdout", "text": ["                   all        314       1191      0.488      0.604      0.548      0.387\n"]}, {"output_type": "stream", "name": "stderr", "text": ["\n"]}, {"output_type": "stream", "name": "stdout", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"output_type": "stream", "name": "stderr", "text": ["     56/100      3.96G     0.9958      1.134      1.193         63        640: 100%|██████████| 126/126 [00:46<00:00,  2.74it/s]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 10/10 [00:03<00:00,  3.04it/s]"]}, {"output_type": "stream", "name": "stdout", "text": ["                   all        314       1191       0.46      0.566      0.521      0.371\n"]}, {"output_type": "stream", "name": "stderr", "text": ["\n"]}, {"output_type": "stream", "name": "stdout", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"output_type": "stream", "name": "stderr", "text": ["     57/100      3.98G      1.016      1.159      1.197         66        640: 100%|██████████| 126/126 [00:46<00:00,  2.73it/s]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 10/10 [00:03<00:00,  2.53it/s]"]}, {"output_type": "stream", "name": "stdout", "text": ["                   all        314       1191      0.457      0.651      0.543      0.387\n"]}, {"output_type": "stream", "name": "stderr", "text": ["\n"]}, {"output_type": "stream", "name": "stdout", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"output_type": "stream", "name": "stderr", "text": ["     58/100      3.99G       1.02      1.173      1.207         49        640: 100%|██████████| 126/126 [00:44<00:00,  2.81it/s]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 10/10 [00:04<00:00,  2.16it/s]"]}, {"output_type": "stream", "name": "stdout", "text": ["                   all        314       1191      0.436      0.582       0.52      0.369\n"]}, {"output_type": "stream", "name": "stderr", "text": ["\n"]}, {"output_type": "stream", "name": "stdout", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"output_type": "stream", "name": "stderr", "text": ["     59/100      4.01G     0.9965      1.142      1.193         99        640: 100%|██████████| 126/126 [00:44<00:00,  2.86it/s]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 10/10 [00:03<00:00,  2.92it/s]"]}, {"output_type": "stream", "name": "stdout", "text": ["                   all        314       1191      0.459      0.593      0.569      0.401\n"]}, {"output_type": "stream", "name": "stderr", "text": ["\n"]}, {"output_type": "stream", "name": "stdout", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"output_type": "stream", "name": "stderr", "text": ["     60/100      4.02G       1.01      1.139      1.196         68        640: 100%|██████████| 126/126 [00:45<00:00,  2.76it/s]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 10/10 [00:03<00:00,  2.97it/s]"]}, {"output_type": "stream", "name": "stdout", "text": ["                   all        314       1191      0.433      0.642      0.524      0.368\n"]}, {"output_type": "stream", "name": "stderr", "text": ["\n"]}, {"output_type": "stream", "name": "stdout", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"output_type": "stream", "name": "stderr", "text": ["     61/100      4.05G      1.003      1.108      1.186         63        640: 100%|██████████| 126/126 [00:45<00:00,  2.75it/s]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 10/10 [00:03<00:00,  2.83it/s]"]}, {"output_type": "stream", "name": "stdout", "text": ["                   all        314       1191      0.402      0.607      0.537      0.385\n"]}, {"output_type": "stream", "name": "stderr", "text": ["\n"]}, {"output_type": "stream", "name": "stdout", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"output_type": "stream", "name": "stderr", "text": ["     62/100      4.06G     0.9946      1.092      1.184         38        640: 100%|██████████| 126/126 [00:44<00:00,  2.82it/s]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 10/10 [00:04<00:00,  2.18it/s]"]}, {"output_type": "stream", "name": "stdout", "text": ["                   all        314       1191      0.437      0.625      0.531      0.371\n"]}, {"output_type": "stream", "name": "stderr", "text": ["\n"]}, {"output_type": "stream", "name": "stdout", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"output_type": "stream", "name": "stderr", "text": ["     63/100      4.08G     0.9811      1.077      1.182         71        640: 100%|██████████| 126/126 [00:44<00:00,  2.82it/s]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 10/10 [00:04<00:00,  2.47it/s]"]}, {"output_type": "stream", "name": "stdout", "text": ["                   all        314       1191      0.511      0.528      0.543      0.386\n"]}, {"output_type": "stream", "name": "stderr", "text": ["\n"]}, {"output_type": "stream", "name": "stdout", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"output_type": "stream", "name": "stderr", "text": ["     64/100      4.09G     0.9733      1.069      1.175         92        640: 100%|██████████| 126/126 [00:44<00:00,  2.80it/s]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 10/10 [00:03<00:00,  2.96it/s]"]}, {"output_type": "stream", "name": "stdout", "text": ["                   all        314       1191      0.477      0.546      0.534      0.382\n"]}, {"output_type": "stream", "name": "stderr", "text": ["\n"]}, {"output_type": "stream", "name": "stdout", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"output_type": "stream", "name": "stderr", "text": ["     65/100      4.12G     0.9919       1.07       1.18         54        640: 100%|██████████| 126/126 [00:45<00:00,  2.75it/s]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 10/10 [00:03<00:00,  2.81it/s]"]}, {"output_type": "stream", "name": "stdout", "text": ["                   all        314       1191      0.494      0.617      0.547      0.387\n"]}, {"output_type": "stream", "name": "stderr", "text": ["\n"]}, {"output_type": "stream", "name": "stdout", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"output_type": "stream", "name": "stderr", "text": ["     66/100      4.13G      0.967      1.058      1.171         73        640: 100%|██████████| 126/126 [00:46<00:00,  2.74it/s]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 10/10 [00:03<00:00,  2.76it/s]"]}, {"output_type": "stream", "name": "stdout", "text": ["                   all        314       1191      0.502       0.57      0.558      0.396\n"]}, {"output_type": "stream", "name": "stderr", "text": ["\n"]}, {"output_type": "stream", "name": "stdout", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"output_type": "stream", "name": "stderr", "text": ["     67/100      4.15G      0.995      1.065      1.179         55        640: 100%|██████████| 126/126 [00:45<00:00,  2.79it/s]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 10/10 [00:04<00:00,  2.21it/s]"]}, {"output_type": "stream", "name": "stdout", "text": ["                   all        314       1191      0.511      0.527      0.539       0.39\n"]}, {"output_type": "stream", "name": "stderr", "text": ["\n"]}, {"output_type": "stream", "name": "stdout", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"output_type": "stream", "name": "stderr", "text": ["     68/100      4.16G     0.9728      1.051      1.176         53        640: 100%|██████████| 126/126 [00:44<00:00,  2.83it/s]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 10/10 [00:04<00:00,  2.38it/s]"]}, {"output_type": "stream", "name": "stdout", "text": ["                   all        314       1191      0.488      0.636      0.566      0.395\n"]}, {"output_type": "stream", "name": "stderr", "text": ["\n"]}, {"output_type": "stream", "name": "stdout", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"output_type": "stream", "name": "stderr", "text": ["     69/100      4.19G     0.9697      1.039      1.169         40        640: 100%|██████████| 126/126 [00:44<00:00,  2.84it/s]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 10/10 [00:03<00:00,  2.92it/s]"]}, {"output_type": "stream", "name": "stdout", "text": ["                   all        314       1191      0.553      0.559      0.546      0.384\n"]}, {"output_type": "stream", "name": "stderr", "text": ["\n"]}, {"output_type": "stream", "name": "stdout", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"output_type": "stream", "name": "stderr", "text": ["     70/100       4.2G     0.9691      1.029      1.162         51        640: 100%|██████████| 126/126 [00:45<00:00,  2.75it/s]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 10/10 [00:03<00:00,  2.81it/s]"]}, {"output_type": "stream", "name": "stdout", "text": ["                   all        314       1191      0.466      0.645      0.537      0.382\n"]}, {"output_type": "stream", "name": "stderr", "text": ["\n"]}, {"output_type": "stream", "name": "stdout", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"output_type": "stream", "name": "stderr", "text": ["     71/100      4.22G     0.9584      1.015      1.168         52        640: 100%|██████████| 126/126 [00:45<00:00,  2.75it/s]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 10/10 [00:03<00:00,  2.81it/s]"]}, {"output_type": "stream", "name": "stdout", "text": ["                   all        314       1191      0.501      0.525      0.529      0.367\n"]}, {"output_type": "stream", "name": "stderr", "text": ["\n"]}, {"output_type": "stream", "name": "stdout", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"output_type": "stream", "name": "stderr", "text": ["     72/100      4.23G     0.9689      1.019      1.168         56        640: 100%|██████████| 126/126 [00:45<00:00,  2.80it/s]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 10/10 [00:04<00:00,  2.25it/s]"]}, {"output_type": "stream", "name": "stdout", "text": ["                   all        314       1191      0.474      0.586      0.538      0.383\n"]}, {"output_type": "stream", "name": "stderr", "text": ["\n"]}, {"output_type": "stream", "name": "stdout", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"output_type": "stream", "name": "stderr", "text": ["     73/100      4.26G     0.9578      1.017      1.164         88        640: 100%|██████████| 126/126 [00:44<00:00,  2.82it/s]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 10/10 [00:03<00:00,  2.96it/s]"]}, {"output_type": "stream", "name": "stdout", "text": ["                   all        314       1191      0.525      0.574      0.536      0.374\n"]}, {"output_type": "stream", "name": "stderr", "text": ["\n"]}, {"output_type": "stream", "name": "stdout", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"output_type": "stream", "name": "stderr", "text": ["     74/100      4.26G     0.9655      1.024      1.164         61        640: 100%|██████████| 126/126 [00:45<00:00,  2.78it/s]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 10/10 [00:03<00:00,  2.82it/s]"]}, {"output_type": "stream", "name": "stdout", "text": ["                   all        314       1191      0.466       0.59      0.551      0.392\n"]}, {"output_type": "stream", "name": "stderr", "text": ["\n"]}, {"output_type": "stream", "name": "stdout", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"output_type": "stream", "name": "stderr", "text": ["     75/100      4.29G      0.976      1.016      1.167         34        640: 100%|██████████| 126/126 [00:45<00:00,  2.78it/s]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 10/10 [00:03<00:00,  2.87it/s]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["                   all        314       1191      0.456       0.65      0.541      0.383\n", "\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"output_type": "stream", "name": "stderr", "text": ["     76/100       4.3G     0.9506          1       1.16         54        640: 100%|██████████| 126/126 [00:44<00:00,  2.84it/s]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 10/10 [00:04<00:00,  2.30it/s]"]}, {"output_type": "stream", "name": "stdout", "text": ["                   all        314       1191      0.506      0.529      0.532      0.377\n"]}, {"output_type": "stream", "name": "stderr", "text": ["\n"]}, {"output_type": "stream", "name": "stdout", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"output_type": "stream", "name": "stderr", "text": ["     77/100      4.32G     0.9472     0.9828      1.154         71        640: 100%|██████████| 126/126 [00:43<00:00,  2.87it/s]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 10/10 [00:03<00:00,  2.68it/s]"]}, {"output_type": "stream", "name": "stdout", "text": ["                   all        314       1191      0.505      0.567      0.548      0.384\n"]}, {"output_type": "stream", "name": "stderr", "text": ["\n"]}, {"output_type": "stream", "name": "stdout", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"output_type": "stream", "name": "stderr", "text": ["     78/100      4.33G      0.947     0.9661      1.161         53        640: 100%|██████████| 126/126 [00:44<00:00,  2.86it/s]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 10/10 [00:03<00:00,  2.91it/s]"]}, {"output_type": "stream", "name": "stdout", "text": ["                   all        314       1191       0.51       0.58      0.551      0.385\n"]}, {"output_type": "stream", "name": "stderr", "text": ["\n"]}, {"output_type": "stream", "name": "stdout", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"output_type": "stream", "name": "stderr", "text": ["     79/100      4.36G     0.9507     0.9807      1.161         51        640: 100%|██████████| 126/126 [00:44<00:00,  2.82it/s]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 10/10 [00:03<00:00,  2.92it/s]"]}, {"output_type": "stream", "name": "stdout", "text": ["                   all        314       1191      0.475      0.617      0.553      0.386\n"]}, {"output_type": "stream", "name": "stderr", "text": ["\n"]}, {"output_type": "stream", "name": "stdout", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"output_type": "stream", "name": "stderr", "text": ["     80/100      4.37G     0.9365     0.9754       1.15         63        640: 100%|██████████| 126/126 [00:43<00:00,  2.89it/s]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 10/10 [00:04<00:00,  2.20it/s]"]}, {"output_type": "stream", "name": "stdout", "text": ["                   all        314       1191      0.446      0.654      0.532      0.377\n"]}, {"output_type": "stream", "name": "stderr", "text": ["\n"]}, {"output_type": "stream", "name": "stdout", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"output_type": "stream", "name": "stderr", "text": ["     81/100      4.39G     0.9147     0.9531      1.149        105        640: 100%|██████████| 126/126 [00:43<00:00,  2.89it/s]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 10/10 [00:03<00:00,  2.76it/s]"]}, {"output_type": "stream", "name": "stdout", "text": ["                   all        314       1191      0.527      0.547      0.536      0.383\n"]}, {"output_type": "stream", "name": "stderr", "text": ["\n"]}, {"output_type": "stream", "name": "stdout", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"output_type": "stream", "name": "stderr", "text": ["     82/100       4.4G     0.9511     0.9601      1.147         50        640: 100%|██████████| 126/126 [00:45<00:00,  2.74it/s]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 10/10 [00:03<00:00,  2.87it/s]"]}, {"output_type": "stream", "name": "stdout", "text": ["                   all        314       1191      0.532      0.565      0.558      0.402\n"]}, {"output_type": "stream", "name": "stderr", "text": ["\n"]}, {"output_type": "stream", "name": "stdout", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"output_type": "stream", "name": "stderr", "text": ["     83/100      4.43G     0.9264     0.9511       1.15         59        640: 100%|██████████| 126/126 [00:44<00:00,  2.82it/s]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 10/10 [00:04<00:00,  2.48it/s]"]}, {"output_type": "stream", "name": "stdout", "text": ["                   all        314       1191       0.51      0.605      0.553      0.387\n"]}, {"output_type": "stream", "name": "stderr", "text": ["\n"]}, {"output_type": "stream", "name": "stdout", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"output_type": "stream", "name": "stderr", "text": ["     84/100      4.43G     0.9316     0.9471      1.154         44        640: 100%|██████████| 126/126 [00:44<00:00,  2.83it/s]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 10/10 [00:04<00:00,  2.20it/s]"]}, {"output_type": "stream", "name": "stdout", "text": ["                   all        314       1191      0.514      0.576      0.553      0.397\n"]}, {"output_type": "stream", "name": "stderr", "text": ["\n"]}, {"output_type": "stream", "name": "stdout", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"output_type": "stream", "name": "stderr", "text": ["     85/100      4.46G     0.9283     0.9491      1.153        106        640: 100%|██████████| 126/126 [00:44<00:00,  2.86it/s]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 10/10 [00:03<00:00,  2.83it/s]"]}, {"output_type": "stream", "name": "stdout", "text": ["                   all        314       1191      0.468       0.62      0.541       0.38\n"]}, {"output_type": "stream", "name": "stderr", "text": ["\n"]}, {"output_type": "stream", "name": "stdout", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"output_type": "stream", "name": "stderr", "text": ["     86/100      4.47G     0.9288     0.9426      1.142         58        640: 100%|██████████| 126/126 [00:45<00:00,  2.78it/s]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 10/10 [00:03<00:00,  2.91it/s]"]}, {"output_type": "stream", "name": "stdout", "text": ["                   all        314       1191      0.478      0.646       0.55      0.385\n"]}, {"output_type": "stream", "name": "stderr", "text": ["\n"]}, {"output_type": "stream", "name": "stdout", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"output_type": "stream", "name": "stderr", "text": ["     87/100      4.49G     0.9323      0.934      1.148         57        640: 100%|██████████| 126/126 [00:45<00:00,  2.76it/s]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 10/10 [00:03<00:00,  2.81it/s]"]}, {"output_type": "stream", "name": "stdout", "text": ["                   all        314       1191      0.532      0.607      0.549       0.39\n"]}, {"output_type": "stream", "name": "stderr", "text": ["\n"]}, {"output_type": "stream", "name": "stdout", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"output_type": "stream", "name": "stderr", "text": ["     88/100       4.5G     0.9387      0.937      1.145         47        640: 100%|██████████| 126/126 [00:44<00:00,  2.85it/s]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 10/10 [00:04<00:00,  2.15it/s]"]}, {"output_type": "stream", "name": "stdout", "text": ["                   all        314       1191      0.495      0.598      0.548      0.381\n"]}, {"output_type": "stream", "name": "stderr", "text": ["\n"]}, {"output_type": "stream", "name": "stdout", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"output_type": "stream", "name": "stderr", "text": ["     89/100      4.53G     0.9167      0.924      1.139         42        640: 100%|██████████| 126/126 [00:43<00:00,  2.89it/s]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 10/10 [00:03<00:00,  2.89it/s]"]}, {"output_type": "stream", "name": "stdout", "text": ["                   all        314       1191      0.519      0.629      0.557      0.385\n"]}, {"output_type": "stream", "name": "stderr", "text": ["\n"]}, {"output_type": "stream", "name": "stdout", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"output_type": "stream", "name": "stderr", "text": ["     90/100      4.54G     0.9177     0.9037      1.131         47        640: 100%|██████████| 126/126 [00:45<00:00,  2.79it/s]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 10/10 [00:03<00:00,  3.11it/s]"]}, {"output_type": "stream", "name": "stdout", "text": ["                   all        314       1191       0.49      0.618      0.548      0.383\n"]}, {"output_type": "stream", "name": "stderr", "text": ["\n"]}, {"output_type": "stream", "name": "stdout", "text": ["Closing dataloader mosaic\n", "\u001b[34m\u001b[1malbumentations: \u001b[0mB<PERSON>r(p=0.01, blur_limit=(3, 7)), MedianBlur(p=0.01, blur_limit=(3, 7)), ToGray(p=0.01, method='weighted_average', num_output_channels=3), CLAHE(p=0.01, clip_limit=(1.0, 4.0), tile_grid_size=(8, 8))\n", "\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"output_type": "stream", "name": "stderr", "text": ["     91/100      4.56G     0.9003     0.9085      1.126         41        640: 100%|██████████| 126/126 [00:46<00:00,  2.74it/s]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 10/10 [00:03<00:00,  2.77it/s]"]}, {"output_type": "stream", "name": "stdout", "text": ["                   all        314       1191      0.471      0.614      0.521      0.365\n"]}, {"output_type": "stream", "name": "stderr", "text": ["\n"]}, {"output_type": "stream", "name": "stdout", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"output_type": "stream", "name": "stderr", "text": ["     92/100      4.57G     0.8764     0.8073      1.111         24        640: 100%|██████████| 126/126 [00:42<00:00,  2.94it/s]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 10/10 [00:04<00:00,  2.28it/s]"]}, {"output_type": "stream", "name": "stdout", "text": ["                   all        314       1191      0.513      0.617       0.54      0.382\n"]}, {"output_type": "stream", "name": "stderr", "text": ["\n"]}, {"output_type": "stream", "name": "stdout", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"output_type": "stream", "name": "stderr", "text": ["     93/100       4.6G     0.8609     0.7893      1.105         21        640: 100%|██████████| 126/126 [00:42<00:00,  2.95it/s]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 10/10 [00:03<00:00,  2.91it/s]"]}, {"output_type": "stream", "name": "stdout", "text": ["                   all        314       1191      0.524      0.524      0.539      0.378\n"]}, {"output_type": "stream", "name": "stderr", "text": ["\n"]}, {"output_type": "stream", "name": "stdout", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"output_type": "stream", "name": "stderr", "text": ["     94/100      4.61G     0.8522     0.7802      1.101         15        640: 100%|██████████| 126/126 [00:43<00:00,  2.87it/s]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 10/10 [00:03<00:00,  2.65it/s]"]}, {"output_type": "stream", "name": "stdout", "text": ["                   all        314       1191      0.474      0.651      0.544      0.383\n"]}, {"output_type": "stream", "name": "stderr", "text": ["\n"]}, {"output_type": "stream", "name": "stdout", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"output_type": "stream", "name": "stderr", "text": ["     95/100      4.63G     0.8488     0.7594        1.1         21        640: 100%|██████████| 126/126 [00:43<00:00,  2.91it/s]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 10/10 [00:04<00:00,  2.20it/s]"]}, {"output_type": "stream", "name": "stdout", "text": ["                   all        314       1191      0.478      0.591      0.536      0.375\n"]}, {"output_type": "stream", "name": "stderr", "text": ["\n"]}, {"output_type": "stream", "name": "stdout", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"output_type": "stream", "name": "stderr", "text": ["     96/100      4.64G      0.846     0.7559      1.097         36        640: 100%|██████████| 126/126 [00:43<00:00,  2.90it/s]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 10/10 [00:03<00:00,  2.86it/s]"]}, {"output_type": "stream", "name": "stdout", "text": ["                   all        314       1191      0.476      0.635      0.533      0.375\n"]}, {"output_type": "stream", "name": "stderr", "text": ["\n"]}, {"output_type": "stream", "name": "stdout", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"output_type": "stream", "name": "stderr", "text": ["     97/100      4.66G     0.8458     0.7611      1.095         38        640: 100%|██████████| 126/126 [00:44<00:00,  2.85it/s]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 10/10 [00:03<00:00,  2.96it/s]"]}, {"output_type": "stream", "name": "stdout", "text": ["                   all        314       1191      0.506      0.605      0.532      0.373\n"]}, {"output_type": "stream", "name": "stderr", "text": ["\n"]}, {"output_type": "stream", "name": "stdout", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"output_type": "stream", "name": "stderr", "text": ["     98/100      4.67G     0.8518     0.7517      1.095         42        640: 100%|██████████| 126/126 [00:42<00:00,  2.95it/s]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 10/10 [00:04<00:00,  2.07it/s]"]}, {"output_type": "stream", "name": "stdout", "text": ["                   all        314       1191      0.465      0.626      0.536      0.378\n"]}, {"output_type": "stream", "name": "stderr", "text": ["\n"]}, {"output_type": "stream", "name": "stdout", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"output_type": "stream", "name": "stderr", "text": ["     99/100       4.7G     0.8423     0.7416      1.099         34        640: 100%|██████████| 126/126 [00:41<00:00,  3.00it/s]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 10/10 [00:03<00:00,  3.02it/s]"]}, {"output_type": "stream", "name": "stdout", "text": ["                   all        314       1191      0.488      0.579      0.537      0.377\n"]}, {"output_type": "stream", "name": "stderr", "text": ["\n"]}, {"output_type": "stream", "name": "stdout", "text": ["\n", "      Epoch    GPU_mem   box_loss   cls_loss   dfl_loss  Instances       Size\n"]}, {"output_type": "stream", "name": "stderr", "text": ["    100/100      4.71G     0.8405     0.7424      1.093         35        640: 100%|██████████| 126/126 [00:44<00:00,  2.85it/s]\n", "                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 10/10 [00:03<00:00,  2.92it/s]"]}, {"output_type": "stream", "name": "stdout", "text": ["                   all        314       1191      0.484      0.633      0.531      0.374\n"]}, {"output_type": "stream", "name": "stderr", "text": ["\n"]}, {"output_type": "stream", "name": "stdout", "text": ["\n", "100 epochs completed in 1.374 hours.\n", "Optimizer stripped from runs/detect/train2/weights/last.pt, 6.3MB\n", "Optimizer stripped from runs/detect/train2/weights/best.pt, 6.3MB\n", "\n", "Validating runs/detect/train2/weights/best.pt...\n", "Ultralytics 8.3.159 🚀 Python-3.11.13 torch-2.6.0+cu124 CUDA:0 (Tesla T4, 15095MiB)\n", "Model summary (fused): 72 layers, 3,011,498 parameters, 0 gradients, 8.1 GFLOPs\n"]}, {"output_type": "stream", "name": "stderr", "text": ["                 Class     Images  Instances      Box(P          R      mAP50  mAP50-95): 100%|██████████| 10/10 [00:05<00:00,  1.89it/s]\n"]}, {"output_type": "stream", "name": "stdout", "text": ["                   all        314       1191      0.436      0.609      0.569        0.4\n", "       Apple Scab Leaf          1          2      0.215        0.5      0.158      0.151\n", "            Apple leaf         24         61      0.551      0.902      0.848      0.585\n", "       Apple rust leaf          4          4      0.347        0.5      0.395      0.171\n", "      Bell_pepper leaf          4         11      0.551       0.56      0.582      0.392\n", " Bell_pepper leaf spot          1          1     0.0621          1      0.995      0.895\n", "        Blueberry leaf          7         41      0.652      0.732      0.802      0.576\n", "           Cherry leaf          2          4     0.0878       0.25      0.125       0.11\n", "      Corn leaf blight         10         12      0.487          1      0.941      0.582\n", "        Corn rust leaf         14         16      0.874       0.75      0.894      0.732\n", "            Peach leaf         25        130      0.799      0.915      0.925      0.621\n", "        Raspberry leaf          3         14      0.363      0.929       0.77      0.536\n", "         Soyabean leaf         22        118       0.76      0.538      0.711      0.549\n", "Squash Powdery mildew leaf          7          9       0.54      0.654      0.672      0.488\n", "       Strawberry leaf         29        146      0.808      0.849       0.91      0.677\n", "Tomato Early blight leaf         11         30      0.214      0.354      0.258      0.158\n", "Tomato Septoria leaf spot         46        128       0.56      0.562      0.554      0.362\n", "           Tomato leaf         16         87      0.443      0.805      0.687      0.319\n", "Tomato leaf bacterial spot          6         16     0.0619       0.25     0.0664      0.048\n", "Tomato leaf late blight         22         52      0.543      0.481      0.447      0.285\n", "Tomato leaf mosaic virus         19        108      0.477      0.157      0.309      0.196\n", "Tomato leaf yellow virus         18        138      0.207      0.355      0.204     0.0899\n", "      Tomato mold leaf         20         60      0.245      0.308      0.226       0.16\n", "            grape leaf          2          3      0.187      0.667      0.598      0.525\n", "Speed: 0.4ms preprocess, 2.6ms inference, 0.0ms loss, 3.8ms postprocess per image\n", "Results saved to \u001b[1mruns/detect/train2\u001b[0m\n"]}]}, {"cell_type": "code", "source": [], "metadata": {"id": "UvzFtvMN0dGp"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": ["model  = YOLO(\"/content/best.pt\")\n", "\n", "results = model.predict(\"/content/PlantDoc-1/test/images\", save = True, stream = True)\n", "\n", "for result in results:\n", "  pass"], "metadata": {"colab": {"base_uri": "https://localhost:8080/"}, "id": "-ymOX5N70dER", "outputId": "a4c258bb-3382-4215-fd82-75f5d2b9b946"}, "execution_count": 5, "outputs": [{"output_type": "stream", "name": "stdout", "text": ["\n", "image 1/246 /content/PlantDoc-1/test/images/-2320-Bacterial-Spot-and-Speck_jpg.rf.7d343cb38f82665c5cc74640e38113ae.jpg: 480x640 3 Tomato Septoria leaf spots, 38.5ms\n", "image 2/246 /content/PlantDoc-1/test/images/0000_jpg.rf.7909c7b24a707518f826749c088758aa.jpg: 640x640 1 grape leaf, 8.2ms\n", "image 3/246 /content/PlantDoc-1/test/images/000_jpg.rf.f448842872e9b0621b2cf0dee28b0d1a.jpg: 480x640 1 Tomato leaf bacterial spot, 7.3ms\n", "image 4/246 /content/PlantDoc-1/test/images/00_jpg.rf.ede21ae21f3bc0c51d23208f29acbbf8.jpg: 480x640 1 Bell_pepper leaf, 1 Bell_pepper leaf spot, 1 Tomato Septoria leaf spot, 6.5ms\n", "image 5/246 /content/PlantDoc-1/test/images/00pe_jpg.rf.9753c2f11401c6e6559723cca688a4d1.jpg: 640x480 1 Blueberry leaf, 1 Cherry leaf, 36.9ms\n", "image 6/246 /content/PlantDoc-1/test/images/01_jpg.rf.b1e1f487896db863e90d9eecd9c39f69.jpg: 512x640 1 Bell_pepper leaf spot, 57.9ms\n", "image 7/246 /content/PlantDoc-1/test/images/02_-Rust-2017-207u24s_jpg.rf.fe7565a09c5a48b8331a60f2f9767478.jpg: 384x640 (no detections), 57.6ms\n", "image 8/246 /content/PlantDoc-1/test/images/02c_jpg.rf.9341af7a548dacf49b74b1f6c2940dda.jpg: 384x640 1 Corn leaf blight, 9.2ms\n", "image 9/246 /content/PlantDoc-1/test/images/039b47d574bc4bb8a14259a1cd96a741_jpg.rf.b3726b14eeb9f2d6a7f5a4f4725c74cb.jpg: 512x640 1 Tomato leaf bacterial spot, 9.1ms\n", "image 10/246 /content/PlantDoc-1/test/images/03gb_jpg.rf.84978188d641b99495f09dace89eaf40.jpg: 416x640 1 grape leaf black rot, 55.6ms\n", "image 11/246 /content/PlantDoc-1/test/images/052609-20<PERSON><PERSON><PERSON>-20Crabapple-20scab-20single-20leaf_JPG_jpg.rf.29f209045861e4b97d5360d96d7859ef.jpg: 480x640 1 Apple Scab Leaf, 10.6ms\n", "image 12/246 /content/PlantDoc-1/test/images/0605_Rust-induced_leafspot_jpg.rf.4f13f24c3d8ca69a122ca33795e81dc4.jpg: 384x640 1 Bell_pepper leaf spot, 10.3ms\n", "image 13/246 /content/PlantDoc-1/test/images/0796_20graylssymt_jpg.rf.39c85823f261eca150ad7bf57166d085.jpg: 544x640 1 Corn Gray leaf spot, 61.3ms\n", "image 14/246 /content/PlantDoc-1/test/images/0796_39maizerust_jpg.rf.52c7c3eebe01df90cb1475398232a030.jpg: 448x640 1 Corn rust leaf, 60.4ms\n", "image 15/246 /content/PlantDoc-1/test/images/0796_40comrust_jpg.rf.c7e3bb35228c3049fe7ca1c0c81eeb82.jpg: 416x640 1 Corn rust leaf, 10.2ms\n", "image 16/246 /content/PlantDoc-1/test/images/0796_47southrust_jpg.rf.2e79494173d59f1464f842bb877cc721.jpg: 384x640 1 Corn leaf blight, 9.9ms\n", "image 17/246 /content/PlantDoc-1/test/images/0796_52srusttelia_jpg.rf.a1f8a7f3ae1d21eda203c7b06891a2dc.jpg: 480x640 1 Corn Gray leaf spot, 11.3ms\n", "image 18/246 /content/PlantDoc-1/test/images/07_17_18-Common_Tomato_Diseases_Canker-258x300_jpg.rf.220d958e54fc565e1ef765422d944c71.jpg: 640x576 1 Tomato leaf bacterial spot, 58.7ms\n", "image 19/246 /content/PlantDoc-1/test/images/07c_jpg.rf.cedf7645ffdb773fb91c9460eedb3c57.jpg: 416x640 1 Corn Gray leaf spot, 8.4ms\n", "image 20/246 /content/PlantDoc-1/test/images/07feb_ma_sbr3_JPG_jpg.rf.d85b7485021f73b447889d708cd88e96.jpg: 448x640 1 Soyabean leaf, 11.3ms\n", "image 21/246 /content/PlantDoc-1/test/images/0_jpg.rf.4eb94c1405d4de9cb48484e0f998bc80.jpg: 480x640 3 Tomato Septoria leaf spots, 8.4ms\n", "image 22/246 /content/PlantDoc-1/test/images/0c_jpg.rf.a43ed60f91e72ed6adb3ccd35407a263.jpg: 288x640 1 Corn leaf blight, 52.8ms\n", "image 23/246 /content/PlantDoc-1/test/images/100983448_jpg.rf.91629e864714c15348dc2463ed304851.jpg: 640x640 1 Tomato Early blight leaf, 9.9ms\n", "image 24/246 /content/PlantDoc-1/test/images/10148582-green-leaf-of-pepper_jpg.rf.65660537c633e26c6f3a6a8d5c259d4a.jpg: 480x640 (no detections), 8.8ms\n", "image 25/246 /content/PlantDoc-1/test/images/11-40580_5_jpg.rf.601c03ff8d357a03f809aa8a97ed34a9.jpg: 320x640 17 Tomato leaf yellow viruss, 53.7ms\n", "image 26/246 /content/PlantDoc-1/test/images/110822-206-Tomato-blight_jpg.rf.5b63d9389e49d3c118f78b2030b03ea2.jpg: 448x640 1 Potato leaf late blight, 1 Tomato leaf late blight, 8.7ms\n", "image 27/246 /content/PlantDoc-1/test/images/12-19striprustJIM_jpg.rf.9ce664e7fcf419e5e42fed328f3ec064.jpg: 640x256 1 Corn rust leaf, 51.0ms\n", "image 28/246 /content/PlantDoc-1/test/images/1234080-Early-Blight_jpg.rf.48dcf66a873ed637326217a7411f86f0.jpg: 640x640 1 Tomato leaf bacterial spot, 8.4ms\n", "image 29/246 /content/PlantDoc-1/test/images/1321189_jpg.rf.6441050092af18d7371f7617a17dfb72.jpg: 224x640 1 Corn Gray leaf spot, 62.0ms\n", "image 30/246 /content/PlantDoc-1/test/images/1355_50commonrust_jpg.rf.d062aac8a3e048453badfb1eec5b981d.jpg: 640x448 1 Corn rust leaf, 62.9ms\n", "image 31/246 /content/PlantDoc-1/test/images/1421_0_jpeg-itok-FMtmgePj_jpg.rf.5231c8c3ed18a572f00544e1d1b9777e.jpg: 480x640 1 Potato leaf early blight, 13.9ms\n", "image 32/246 /content/PlantDoc-1/test/images/160314_web_jpg.rf.e523b60db518848122d18347e8452748.jpg: 640x480 1 Corn leaf blight, 13.0ms\n", "image 33/246 /content/PlantDoc-1/test/images/1684_jpg.rf.afc2a2159117a60e9a66c52ee0f9652a.jpg: 640x576 4 Tom<PERSON> leafs, 9.3ms\n", "image 34/246 /content/PlantDoc-1/test/images/17fc47_jpg.rf.1fb27f9e9be8b77c132c90e9acde4c68.jpg: 640x480 12 Tomato leafs, 3 Tomato mold leafs, 14.1ms\n", "image 35/246 /content/PlantDoc-1/test/images/185161-004-EAF28842_jpg.rf.33544235dab18ec8663966a76455db34.jpg: 640x608 1 grape leaf black rot, 57.1ms\n", "image 36/246 /content/PlantDoc-1/test/images/18c_jpg.rf.63522546f7c2e9b3c55dccfa7ce373ac.jpg: 480x640 1 Tomato leaf late blight, 10.4ms\n", "image 37/246 /content/PlantDoc-1/test/images/1b321015-6e33-4f18-aade-888f4383fe92_jpeg_jpg.rf.3f50119d3a0426a0ebbea8b20a96a370.jpg: 448x640 1 Apple Scab Leaf, 9.5ms\n", "image 38/246 /content/PlantDoc-1/test/images/20090710-lateblight_jpg.rf.5bbb61bee5fc101c2355fa95a4f93a87.jpg: 480x640 1 Tomato mold leaf, 10.4ms\n", "image 39/246 /content/PlantDoc-1/test/images/2011-011_jpg.rf.3d77e6d281485229c4c8f65b43380984.jpg: 480x640 1 grape leaf black rot, 8.1ms\n", "image 40/246 /content/PlantDoc-1/test/images/2013-08-20-06_jpg.rf.e8d5621ef9dc96e860445b0d541769fe.jpg: 480x640 3 Tomato leafs, 1 Tomato leaf late blight, 7.8ms\n", "image 41/246 /content/PlantDoc-1/test/images/20130519cedarapplerust_jpg.rf.ce0a5ba06880783806d5c264a83d1209.jpg: 480x640 1 Apple rust leaf, 9.6ms\n", "image 42/246 /content/PlantDoc-1/test/images/20130610_110514_jpg.rf.c4bb19a9976eaed15e53a8e9ea4d0b25.jpg: 480x640 1 Apple rust leaf, 11.6ms\n", "image 43/246 /content/PlantDoc-1/test/images/20130802_111632_jpg.rf.3ac26fc6309b2f563e007109d694dc53.jpg: 480x640 (no detections), 11.0ms\n", "image 44/246 /content/PlantDoc-1/test/images/2013Corn_GrayLeafSpot_0815_0003_JPG_jpg.rf.c7ecdd54de1635af62a48a4c08773cff.jpg: 480x640 1 Corn Gray leaf spot, 13.3ms\n", "image 45/246 /content/PlantDoc-1/test/images/2015070295153021_jpg.rf.a36b4c2bb2859cd81a0cef2748f7b752.jpg: 640x480 1 Corn leaf blight, 15.2ms\n", "image 46/246 /content/PlantDoc-1/test/images/20180511_090912-14gtw8a-e1526047952754_jpg.rf.7d28f90b37f6dd54259ed0708dce5a43.jpg: 640x384 1 Cherry leaf, 35.8ms\n", "image 47/246 /content/PlantDoc-1/test/images/20180511_091133-24l1vhg-e1526047988236_jpg.rf.11a51f80d69461751669a9d71c5c13dd.jpg: 640x384 2 Apple leafs, 7.1ms\n", "image 48/246 /content/PlantDoc-1/test/images/20180511_091252-1gy5xf5-e1526048000596_jpg.rf.1158e8dd1bf3c9f22037d3fc71f1ee83.jpg: 640x384 1 Apple leaf, 6.9ms\n", "image 49/246 /content/PlantDoc-1/test/images/2256-body-1501555581-1_jpg.rf.b4d84e80d43ccbc69c013f9ec675772a.jpg: 480x640 1 Corn rust leaf, 7.4ms\n", "image 50/246 /content/PlantDoc-1/test/images/2540_600_jpg.rf.3b1c2d46c5e20609efbb12d60315846a.jpg: 640x544 1 Blueberry leaf, 1 Potato leaf late blight, 1 Tomato leaf, 40.1ms\n", "image 51/246 /content/PlantDoc-1/test/images/28-500x375_jpg.rf.f40d4a0aa278122f5054b0fb66861323.jpg: 480x640 2 Apple Scab Leafs, 9.4ms\n", "image 52/246 /content/PlantDoc-1/test/images/2STEM_jpg.rf.d1f00c64801e71246cc88c29457769b5.jpg: 640x384 1 Tomato leaf bacterial spot, 10.7ms\n", "image 53/246 /content/PlantDoc-1/test/images/2septoria-tomato_jpg.rf.171d48b66c3240c351bb93026f62683a.jpg: 448x640 3 Tomato Septoria leaf spots, 7.0ms\n", "image 54/246 /content/PlantDoc-1/test/images/3023_jpg.rf.0c3e9120bf303dbb5b2ac7ae36ebe506.jpg: 480x640 5 Potato leaf late blights, 6.8ms\n", "image 55/246 /content/PlantDoc-1/test/images/35589125035_662dd5b258_b_jpg.rf.8c5867e11cbdd698a41042969f751ba4.jpg: 640x480 1 grape leaf, 1 grape leaf black rot, 7.1ms\n", "image 56/246 /content/PlantDoc-1/test/images/393_jpg.rf.6ae4409682e7254bfad08e83163979d0.jpg: 448x640 1 Corn rust leaf, 7.4ms\n", "image 57/246 /content/PlantDoc-1/test/images/4-apple-rust2_19011_matt-bertone_jpg.rf.18a6b156aa333ba53fa7e3716a3fb556.jpg: 448x640 1 Apple rust leaf, 1 Bell_pepper leaf spot, 6.3ms\n", "image 58/246 /content/PlantDoc-1/test/images/4120978-single-green-leaf-of-apple-tree_jpg.rf.6c3fd7047c92c50e5c631dd27b975aa4.jpg: 480x640 1 Apple leaf, 7.3ms\n", "image 59/246 /content/PlantDoc-1/test/images/4825902504_5fa80b65b8_z_jpg.rf.f162b78991e64c06355df855954b33b7.jpg: 448x640 1 Blueberry leaf, 1 Soyabean leaf, 7.1ms\n", "image 60/246 /content/PlantDoc-1/test/images/49-20Leafmold-20Bottom_jpg.rf.afc498a8b112bd5950d0b1533c256091.jpg: 352x640 1 Tomato mold leaf, 36.5ms\n", "image 61/246 /content/PlantDoc-1/test/images/5-29black-rot-chardRR_jpg.rf.9e1ce24284be31a7c902c759835e2241.jpg: 448x640 1 grape leaf black rot, 8.0ms\n", "image 62/246 /content/PlantDoc-1/test/images/50-20Leafmold-20Top_jpg.rf.522b83b57a1dd907cfe2b564991fa060.jpg: 416x640 1 Tomato mold leaf, 7.5ms\n", "image 63/246 /content/PlantDoc-1/test/images/5496239405_d95bdb97d1_z_jpg.rf.362c8c8e1c9980bc52a988357b51861f.jpg: 480x640 1 Corn rust leaf, 7.1ms\n", "image 64/246 /content/PlantDoc-1/test/images/55830b60a7cf2_image_jpg.rf.93a1d446f3d81e6dc403ec12072d01e2.jpg: 640x448 3 Tomato Early blight leafs, 2 Tomato Septoria leaf spots, 7.2ms\n", "image 65/246 /content/PlantDoc-1/test/images/5816740026_d42ef24413_Phytophthora-Infestans_jpg.rf.bdcd5d1e4fe578db6877eb4617600f30.jpg: 640x448 1 Tomato mold leaf, 6.8ms\n", "image 66/246 /content/PlantDoc-1/test/images/6134794031202304_jpeg_jpg.rf.2f1579e314da6cac18bdb0140a9d7e3c.jpg: 640x480 2 Tomato Septoria leaf spots, 1 Tomato leaf, 1 Tomato leaf bacterial spot, 7.5ms\n", "image 67/246 /content/PlantDoc-1/test/images/636227737977191231-septoria-leaf-spot-VT_jpg.rf.aaab465af19005f0666fae7cc3f2c36d.jpg: 512x640 3 Tomato Septoria leaf spots, 2 Tomato leaf bacterial spots, 7.1ms\n", "image 68/246 /content/PlantDoc-1/test/images/636368370164094909-late-blight-15-_jpg.rf.2b9ba4569f350ff98c9b854b8133000e.jpg: 480x640 1 Tomato leaf, 1 Tomato leaf late blight, 1 Tomato mold leaf, 7.6ms\n", "image 69/246 /content/PlantDoc-1/test/images/6447731_orig_jpg.rf.4395976bcedcadf9f3353eae980f1f23.jpg: 448x640 1 Tomato Septoria leaf spot, 1 Tomato leaf bacterial spot, 7.2ms\n", "image 70/246 /content/PlantDoc-1/test/images/7-17-Photo3_Septoria-MARY_jpg.rf.b6ca88add66a78e8ff6e3803617ed1dd.jpg: 512x640 1 Tomato Septoria leaf spot, 3 Tomato leaf bacterial spots, 7.0ms\n", "image 71/246 /content/PlantDoc-1/test/images/70_jpg.rf.d232187c8c6304dc6d6353aba46c3f40.jpg: 640x640 1 Tomato mold leaf, 8.1ms\n", "image 72/246 /content/PlantDoc-1/test/images/7190f27ead55c0f6e3ff8b982972810a5446a9713a49d_1260x1260_jpg.rf.739519c6b75ae2e0207d3bd58e714a4b.jpg: 640x544 1 Peach leaf, 12.4ms\n", "image 73/246 /content/PlantDoc-1/test/images/730-grape-leaf-2560x1600-nature-wallpaper_jpg.rf.342f3460eec37eb5ed77809f0b513cf2.jpg: 416x640 1 grape leaf, 7.5ms\n", "image 74/246 /content/PlantDoc-1/test/images/80104747_jpg.rf.7f3fc9c8b28a11d93ccfca3705708430.jpg: 640x576 1 Corn Gray leaf spot, 1 Corn rust leaf, 8.8ms\n", "image 75/246 /content/PlantDoc-1/test/images/816_jpg.rf.f2f250e433bcf898288c8f998b6f70d8.jpg: 544x640 2 Apple Scab Leafs, 8.0ms\n", "image 76/246 /content/PlantDoc-1/test/images/8226402877_9abf151b5b_b_jpg.rf.876a6172fd6ee0f0da7e67f4efdcebdb.jpg: 448x640 9 Tomato leaf yellow viruss, 7.4ms\n", "image 77/246 /content/PlantDoc-1/test/images/90e2c0_jpg.rf.b9d4c3f0bc3b28235940253910083267.jpg: 640x480 1 <PERSON><PERSON> leaf, 7.8ms\n", "image 78/246 /content/PlantDoc-1/test/images/9343310-small_jpg.rf.adbbc256f4ed6610933e7418c6cda241.jpg: 640x512 1 Apple Scab Leaf, 1 Apple rust leaf, 36.8ms\n", "image 79/246 /content/PlantDoc-1/test/images/9511_img_jpg.rf.e783a63413a71a436470f2e5a6034658.jpg: 480x640 14 Tomato leafs, 7.2ms\n", "image 80/246 /content/PlantDoc-1/test/images/99e886623c2080c22f6519b0e708c531_jpg.rf.f7f90eebe4f2d17ae209ce53bf1640a0.jpg: 448x640 2 Apple rust leafs, 7.0ms\n", "image 81/246 /content/PlantDoc-1/test/images/Apple-Leaf-Wallpaper-17_jpg.rf.34dcdf624917c2ff3f9b5cca6b303f27.jpg: 384x640 (no detections), 7.0ms\n", "image 82/246 /content/PlantDoc-1/test/images/B2750109-Late_blight_on_a_potato_plant-SPL_jpg.rf.630103c8ef35e339f979e47dd0281642.jpg: 640x448 (no detections), 7.1ms\n", "image 83/246 /content/PlantDoc-1/test/images/BIGSD_jpg.rf.c55a7bb1d0c3e0976b6dfa1a4e11de3d.jpg: 384x640 1 Corn leaf blight, 3 Tomato mold leafs, 8.2ms\n", "image 84/246 /content/PlantDoc-1/test/images/Bacterial-spot-pepper2_jpg.rf.cb9376f97f15a0ec01624e9f6b29da78.jpg: 512x640 1 Bell_pepper leaf spot, 7.2ms\n", "image 85/246 /content/PlantDoc-1/test/images/Bacterial_spots563_jpg.rf.4146fd59b089ec18250cfa7f97a0378c.jpg: 480x640 2 Tomato leaf bacterial spots, 6.9ms\n", "image 86/246 /content/PlantDoc-1/test/images/Black-20rot-20on-20foliage2_jpg.rf.b2887630b93e342709bcdece6befc16d.jpg: 480x640 1 Squash Powdery mildew leaf, 1 grape leaf, 1 grape leaf black rot, 6.3ms\n", "image 87/246 /content/PlantDoc-1/test/images/Black-20rot-20on-20foliage_jpg.rf.824235c9d6086ddb6baf00d1ee3d7037.jpg: 480x640 3 grape leaf black rots, 6.5ms\n", "image 88/246 /content/PlantDoc-1/test/images/CMVpepperLeafShock-copy-50QUALITY-1ge8umw_jpg.rf.f4dce10dbddb176c1f24c234d114ec94.jpg: 480x640 1 Bell_pepper leaf spot, 5 Blueberry leafs, 3 Soyabean leafs, 6.3ms\n", "image 89/246 /content/PlantDoc-1/test/images/Common-20Diseases-20Figure-201_JPG_jpg.rf.6824a34e7122ebd635ff756369d87633.jpg: 640x384 1 Corn leaf blight, 7.2ms\n", "image 90/246 /content/PlantDoc-1/test/images/Corn-SCLB-2017-1_jpg.rf.f14a1c6073cd7b91c7158c422251d37f.jpg: 640x448 1 Corn Gray leaf spot, 1 Corn leaf blight, 9.8ms\n", "image 91/246 /content/PlantDoc-1/test/images/D-29AlfMov_jpg.rf.7575ed148d33b3a3311a0a66656f306d.jpg: 512x640 2 Tomato mold leafs, 7.9ms\n", "image 92/246 /content/PlantDoc-1/test/images/DSCN1015_JPG_jpg.rf.6de2425f06e7518f73c6df91005d3a1b.jpg: 480x640 6 Bell_pepper leafs, 7.1ms\n", "image 93/246 /content/PlantDoc-1/test/images/DSCN7317_JPG_jpg.rf.1f65c237c38da756f5b822e0709ae51b.jpg: 640x640 1 Apple leaf, 1 grape leaf, 8.3ms\n", "image 94/246 /content/PlantDoc-1/test/images/E-29Tomato-late-blight-foliar_jpg.rf.85a533927322b76690bab3307654b88f.jpg: 480x640 1 Tomato mold leaf, 7.3ms\n", "image 95/246 /content/PlantDoc-1/test/images/GREEN-20BELL-20PLANT-20YELLOW-20LEAF_JPG_jpg.rf.9130b09fb9fab36ef9e4b0631f8d0f17.jpg: 480x640 2 Bell_pepper leafs, 1 Soyabean leaf, 6.5ms\n", "image 96/246 /content/PlantDoc-1/test/images/H5w92_jpg.rf.12d2f2502b26b71fed70b578fbd3b09d.jpg: 640x480 1 Bell_pepper leaf, 7.1ms\n", "image 97/246 /content/PlantDoc-1/test/images/IMG_1246_jpg.rf.550608c418f83532d3abbc27f0c9ce1b.jpg: 480x640 1 Blueberry leaf, 1 Tomato mold leaf, 7.5ms\n", "image 98/246 /content/PlantDoc-1/test/images/IMG_1629_JPG-1507122477_jpg.rf.a080c683f4cf687c8b7814148c563a41.jpg: 640x480 2 Bell_pepper leafs, 13.1ms\n", "image 99/246 /content/PlantDoc-1/test/images/IMG_42231_jpg.rf.29ae41fc249871ff482b94843f37dd92.jpg: 480x640 1 Corn leaf blight, 1 Corn rust leaf, 7.1ms\n", "image 100/246 /content/PlantDoc-1/test/images/IMG_5808_jpg.rf.aae961c579d7bc4b1401dba36d080d8d.jpg: 480x640 1 Tomato leaf late blight, 1 Tomato mold leaf, 6.9ms\n", "image 101/246 /content/PlantDoc-1/test/images/LateBlight04_jpg.rf.80a864e6bf6278659e1d0d67f82d6048.jpg: 448x640 1 Potato leaf early blight, 2 Potato leaf late blights, 1 Soyabean leaf, 7.5ms\n", "image 102/246 /content/PlantDoc-1/test/images/LateBlt09_06_jpg.rf.daa06bf866804db4277ec509b9908602.jpg: 448x640 2 Tomato leaf late blights, 7.8ms\n", "image 103/246 /content/PlantDoc-1/test/images/P1130286_jpg.rf.24bda54896654c8eaef3c5704f8e8c96.jpg: 640x480 1 Apple leaf, 1 grape leaf, 7.0ms\n", "image 104/246 /content/PlantDoc-1/test/images/P1200665-Vaccinium-alaskaense-vs-ovalifolium-leaf-and-twig-cr_jpg.rf.f55b57ad346e7838825c20ca6f32732a.jpg: 480x640 2 Blueberry leafs, 1 Cherry leaf, 7.5ms\n", "image 105/246 /content/PlantDoc-1/test/images/P1200701-Vaccinium-ovalifolium-Oval-leaf-Blueberry-leaf-margin-<PERSON>-<PERSON>-cr_jpg.rf.209d0e3405b05ebbd4f093cf62626810.jpg: 640x480 1 Bell_pepper leaf, 7.8ms\n", "image 106/246 /content/PlantDoc-1/test/images/Powdery-Mildew-on-squash_jpg.rf.a12f8e061f5fad878cc636bce1ee2022.jpg: 480x640 1 Squash Powdery mildew leaf, 7.0ms\n", "image 107/246 /content/PlantDoc-1/test/images/RARZ4_jpg.rf.8ff06fb3aec5bd3d7b914b303b7f3a58.jpg: 448x640 1 Tomato Septoria leaf spot, 1 Tomato leaf bacterial spot, 1 Tomato leaf late blight, 7.7ms\n", "image 108/246 /content/PlantDoc-1/test/images/Raspberry-leaf_jpg.rf.04ed86ea6930809b440f8acb60078c91.jpg: 448x640 3 Raspberry leafs, 7.4ms\n", "image 109/246 /content/PlantDoc-1/test/images/RnGnC_jpg.rf.c66550dbc4be94edacb8567edb511af9.jpg: 448x640 1 Apple leaf, 1 Bell_pepper leaf, 1 Blueberry leaf, 1 Soyabean leaf, 7.3ms\n", "image 110/246 /content/PlantDoc-1/test/images/Shoemaker_7068_JPG_jpg.rf.065ba58178c60de4dcdc410a36e71fbc.jpg: 480x640 1 Tomato Early blight leaf, 1 Tomato leaf late blight, 7.3ms\n", "image 111/246 /content/PlantDoc-1/test/images/Soybean-20edamame-20leaf1_JPG_jpg.rf.101bc27698461c29e8de2e67fdc34f08.jpg: 512x640 3 Soyabean leafs, 11.1ms\n", "image 112/246 /content/PlantDoc-1/test/images/Tom_Bact3_jpg.rf.0ae29b36027e8bce58bdc600e9a6b6bf.jpg: 384x640 1 Potato leaf late blight, 7.8ms\n", "image 113/246 /content/PlantDoc-1/test/images/Tom_Bact7_jpg.rf.8036593ca2c55701970d69dc135321e0.jpg: 448x640 1 Potato leaf early blight, 2 Tomato leaf bacterial spots, 7.3ms\n", "image 114/246 /content/PlantDoc-1/test/images/Tomato-2BBacterial-2BSpot-2Bon-2BLeaves_jpg.rf.be7e4bf55b27254027e114d9b1de07d6.jpg: 352x640 1 Tomato Septoria leaf spot, 1 Tomato leaf bacterial spot, 8.6ms\n", "image 115/246 /content/PlantDoc-1/test/images/Tomato-Mosaic-1wdr2jx-300x225_jpg.rf.afdeee77d7740f2b91fe2982c8d5ffc3.jpg: 352x640 1 Tomato mold leaf, 6.6ms\n", "image 116/246 /content/PlantDoc-1/test/images/Tomato59_JPG_jpg.rf.efb0c7b861080d1087993eb85ee3d9cd.jpg: 512x640 3 Tomato leafs, 7.1ms\n", "image 117/246 /content/PlantDoc-1/test/images/Tomato_leaf_jpg.rf.58ea97da630e2a7ca28e32fe0c8f21ea.jpg: 640x640 2 Tomato mold leafs, 8.6ms\n", "image 118/246 /content/PlantDoc-1/test/images/apple-20scab-20leaf_jpg.rf.99e15aa4e40c83b138d52dc9cba07e80.jpg: 640x384 1 Apple Scab Leaf, 7.7ms\n", "image 119/246 /content/PlantDoc-1/test/images/apple-20scabnew_jpg.rf.098f1b75bf08b8e0e06f0fa41288c38e.jpg: 512x640 1 Blueberry leaf, 7.9ms\n", "image 120/246 /content/PlantDoc-1/test/images/apple-leaf-14319997_jpg.rf.6d9626f38ceaa6e23bb49a4797498b23.jpg: 608x640 1 Apple leaf, 35.7ms\n", "image 121/246 /content/PlantDoc-1/test/images/apple-leaf-9834637_jpg.rf.b9b24d350139ea6f5820db53fe0df72b.jpg: 480x640 1 Apple leaf, 7.5ms\n", "image 122/246 /content/PlantDoc-1/test/images/apple-leaf-closeup-37636177_jpg.rf.7496a800096fb7b47e909735d5162e27.jpg: 480x640 1 Apple leaf, 6.8ms\n", "image 123/246 /content/PlantDoc-1/test/images/apple-leaf-isolated-white-background-56631026_jpg.rf.ba9a61922a5ebcd0438e53239914546f.jpg: 576x640 1 Apple leaf, 39.1ms\n", "image 124/246 /content/PlantDoc-1/test/images/apple-scab-5366820_jpg.rf.5945dd622abf0dd1b5d31febb3b0a377.jpg: 544x640 2 Apple Scab Leafs, 7.9ms\n", "image 125/246 /content/PlantDoc-1/test/images/apples_apple-scab_01_zoom_jpg.rf.3d63cbdaf8bd1d69edddaa4aab3dec16.jpg: 512x640 1 Apple Scab Leaf, 1 Cherry leaf, 7.8ms\n", "image 126/246 /content/PlantDoc-1/test/images/apples_apple-scab_02_thm_jpg.rf.5b57a2e9beae5d54243196efaf8d5fc5.jpg: 480x640 1 Tomato mold leaf, 7.1ms\n", "image 127/246 /content/PlantDoc-1/test/images/apples_apple-scab_10_zoom_jpg.rf.df3705f34ee29eb1018ed31803f7c559.jpg: 480x640 1 Apple leaf, 10.5ms\n", "image 128/246 /content/PlantDoc-1/test/images/backus-056-potato-blight_jpg.rf.d171057ab69b8eff5539f19da8a5a3eb.jpg: 480x640 4 Potato leaf late blights, 6.3ms\n", "image 129/246 /content/PlantDoc-1/test/images/bact-spot-fig-1_jpg.rf.d43d7c22ad12c7ab2e18e61208486803.jpg: 448x640 2 Bell_pepper leaf spots, 8.2ms\n", "image 130/246 /content/PlantDoc-1/test/images/bacterial_leaf_spot_pepper_l_jpg.rf.9b79517d9d00eda341b3ab67987d1b0c.jpg: 480x640 1 Bell_pepper leaf spot, 1 Tomato leaf bacterial spot, 7.4ms\n", "image 131/246 /content/PlantDoc-1/test/images/black_leaf_mold_in_zina_tina_jpg.rf.a4bec1e56bfb9870cc95c1b3bdf78ac1.jpg: 480x640 1 Peach leaf, 2 Tomato mold leafs, 7.3ms\n", "image 132/246 /content/PlantDoc-1/test/images/blueberry-leaves-normal-above-and-iron-deficient-below-bgahf8_jpg.rf.4c0edcdc42895d4edf44a3a2e10a1d20.jpg: 640x480 2 Blueberry leafs, 1 Soyabean leaf, 8.1ms\n", "image 133/246 /content/PlantDoc-1/test/images/blueberrysilverleaf16-1372b_jpg.rf.f18a2274861711aabcaf0077fff0ae33.jpg: 512x640 2 Blueberry leafs, 2 Soyabean leafs, 8.6ms\n", "image 134/246 /content/PlantDoc-1/test/images/brleaf2_zoom_jpg.rf.f4b874692d0e8ea36e103752ddb2db5b.jpg: 480x640 1 grape leaf black rot, 7.4ms\n", "image 135/246 /content/PlantDoc-1/test/images/bugs-and-blight-061_jpg.rf.9e4facef93f9fefd8dc15d3a0e89a5ff.jpg: 480x640 1 Tomato Early blight leaf, 1 Tomato Septoria leaf spot, 4 Tomato leafs, 6.4ms\n", "image 136/246 /content/PlantDoc-1/test/images/corn-disease-update-fig-3-gray-leaf-spot_jpg.rf.a1770006a385b28a6a221864fc1ca9d3.jpg: 480x640 1 Corn leaf blight, 6.8ms\n", "image 137/246 /content/PlantDoc-1/test/images/corn-gray-leaf-spot-f4_jpg.rf.0c6c8746a18d8896c84c83b68953c88c.jpg: 640x544 1 Corn Gray leaf spot, 1 Corn leaf blight, 8.1ms\n", "image 138/246 /content/PlantDoc-1/test/images/d7b79dbc0bca0eb3e63fbe5dfb256ccd53d90a4eb1413_1260x1260_jpg.rf.1ff1926f68618b1109d952f0a6875d63.jpg: 640x544 1 Peach leaf, 7.2ms\n", "image 139/246 /content/PlantDoc-1/test/images/depositphotos_1323264-Raspberry-leaf-on-white_jpg.rf.d31fccec4af7b6273e830571fe7cc57f.jpg: 512x640 1 Raspberry leaf, 7.2ms\n", "image 140/246 /content/PlantDoc-1/test/images/depositphotos_1323551-stock-photo-raspberry-leaves_jpg.rf.c18d917f7105f692ba42a9dacf7cbe5d.jpg: 640x640 2 Raspberry leafs, 2 Strawberry leafs, 10.5ms\n", "image 141/246 /content/PlantDoc-1/test/images/depositphotos_2795312-stock-photo-green-raspberry-leaf_jpg.rf.31cdb19532420c7df54d4ad6ab5447f1.jpg: 480x640 3 Raspberry leafs, 7.2ms\n", "image 142/246 /content/PlantDoc-1/test/images/depositphotos_3443387-stock-photo-the-green-grape-leaf-on_jpg.rf.5f106a02bebe93b94e8ad9ee1c760cd7.jpg: 480x640 1 grape leaf, 6.4ms\n", "image 143/246 /content/PlantDoc-1/test/images/depositphotos_3786198-Peach-leaf_jpg.rf.a6b6b1129adfe52a46fc3fe76cae5340.jpg: 640x640 1 Peach leaf, 8.1ms\n", "image 144/246 /content/PlantDoc-1/test/images/depositphotos_6898486-stock-photo-grape-leaves_jpg.rf.cb9e6c5733d6637f550ea13de2935bed.jpg: 480x640 1 grape leaf, 7.2ms\n", "image 145/246 /content/PlantDoc-1/test/images/dsc_8148cs_jpg.rf.cf7e6ce782b424c4276440072c2aaeb3.jpg: 640x640 1 Tomato Early blight leaf, 8.1ms\n", "image 146/246 /content/PlantDoc-1/test/images/dscn3175_jpg.rf.5a89915a0c1bfa46fd18813464bb57a3.jpg: 480x640 2 Tomato Early blight leafs, 6 Tomato leaf late blights, 11.2ms\n", "image 147/246 /content/PlantDoc-1/test/images/dscn6689_jpg.rf.598f61e9df5e78e02dfc2f23b023068e.jpg: 480x640 1 Potato leaf late blight, 3 Tomato mold leafs, 6.9ms\n", "image 148/246 /content/PlantDoc-1/test/images/e33155_jpg.rf.995a79b5adcd555f82800c2f5030904f.jpg: 480x640 6 Bell_pepper leafs, 6.4ms\n", "image 149/246 /content/PlantDoc-1/test/images/e52bbb9a08ede2948ba0c006f02fe7b053ad7831d1df6_1260x1260_jpg.rf.7a938a8078a15c6f4c2e88f181e4d09b.jpg: 640x544 4 Cherry leafs, 7.9ms\n", "image 150/246 /content/PlantDoc-1/test/images/early-blight-alternaria-alternata-leaf-spotting-on-potato-leaf-X91XJE_jpg.rf.438cff977d4783e8a77456efd048f1e4.jpg: 608x640 1 Potato leaf early blight, 8.2ms\n", "image 151/246 /content/PlantDoc-1/test/images/early-blight-or-target-spot-alternaria-solani-lesions-on-a-tomato-AXK6AY_jpg.rf.225bd7168f65cb020a4f150e5f115b75.jpg: 544x640 1 Tomato leaf late blight, 8.0ms\n", "image 152/246 /content/PlantDoc-1/test/images/early-blight-septoria-ls-fig-3_jpg.rf.b2ec144c28074f285560527e52203290.jpg: 480x640 1 Tomato Septoria leaf spot, 1 grape leaf black rot, 7.2ms\n", "image 153/246 /content/PlantDoc-1/test/images/early_blight1-150x150_jpg.rf.619c0e7fd8102924607913eaca3ac544.jpg: 640x640 1 Tomato mold leaf, 8.1ms\n", "image 154/246 /content/PlantDoc-1/test/images/earlyblight21__jpg.rf.542e6341a9730d3dd3a85b400aaa8af9.jpg: 640x480 1 Peach leaf, 1 Raspberry leaf, 1 Tomato leaf, 1 Tomato leaf late blight, 7.4ms\n", "image 155/246 /content/PlantDoc-1/test/images/earlyblightpotato_jpg.rf.fac4ab62b8aae5e307f615807783285f.jpg: 512x640 1 Potato leaf early blight, 7.7ms\n", "image 156/246 /content/PlantDoc-1/test/images/ed12ff77ce021883de241830dee55e2e53d2c93231699_1260x1260_jpg.rf.c453cfe3bdb0693c775ebc98dd7680ed.jpg: 640x544 1 Cherry leaf, 7.9ms\n", "image 157/246 /content/PlantDoc-1/test/images/everystockphoto-21099455-l_jpg.rf.61c38274fe96179cfeefbdd1f61b3de9.jpg: 480x640 1 Bell_pepper leaf, 7.4ms\n", "image 158/246 /content/PlantDoc-1/test/images/fc729af279e9087d00bd32d89a1552a756a3ad9de7f2b_1260x1260_jpg.rf.cbd278777d5f66ee1a70247c0208614f.jpg: 640x544 1 Apple leaf, 7.9ms\n", "image 159/246 /content/PlantDoc-1/test/images/five-cherry-tree-green-leaves-white-isolated-background-34493587_jpg.rf.d60ca9971f3156317b1636f13252eec4.jpg: 640x512 4 Apple leafs, 7.0ms\n", "image 160/246 /content/PlantDoc-1/test/images/fresh-soy-leaves-isolated-on-260nw-667193923_jpg.rf.c7049fa418aee02f76c23c98ed97b091.jpg: 576x640 1 Peach leaf, 9.5ms\n", "image 161/246 /content/PlantDoc-1/test/images/fungus-univ-of-minnesoeta_jpg.rf.615aac80ce5e69e17e22d214a6d3b80a.jpg: 480x640 3 Tomato leaf bacterial spots, 1 Tomato leaf late blight, 5 Tomato mold leafs, 8.0ms\n", "image 162/246 /content/PlantDoc-1/test/images/fwa2013313115151_jpg.rf.b4b3ea80ebd89dede7aff88793afcf31.jpg: 480x640 3 Tomato leaf mosaic viruss, 10 Tomato leaf yellow viruss, 6.2ms\n", "image 163/246 /content/PlantDoc-1/test/images/glyphosate_jpg.rf.d49a3e812582c812052ffd60433bc6a9.jpg: 320x640 4 Tomato leaf yellow viruss, 7.3ms\n", "image 164/246 /content/PlantDoc-1/test/images/grape-leaf-14309623_jpg.rf.97456993f66540fe8d29859daba121b1.jpg: 608x640 1 grape leaf, 8.2ms\n", "image 165/246 /content/PlantDoc-1/test/images/grape-leaf-picture-id119212425-k-6-m-119212425-s-612x612-w-0-h-8RZBWGcvXe4tAwhhvYZVFzWyRyiN5iTxKfWBpPT1FcU-_jpg.rf.1b33d404248ebf0ad6211880f69b9eb8.jpg: 640x512 1 grape leaf, 7.6ms\n", "image 166/246 /content/PlantDoc-1/test/images/grape_F15a_jpg.rf.a2be7bd58fdc029f875797963149c2ee.jpg: 512x640 1 Tomato Septoria leaf spot, 2 grape leaf black rots, 7.0ms\n", "image 167/246 /content/PlantDoc-1/test/images/grapes-leaves_jpg.rf.edd68ff8665a21eb2c4c548b44afa193.jpg: 480x640 1 Strawberry leaf, 4 grape leafs, 6.9ms\n", "image 168/246 /content/PlantDoc-1/test/images/grapevine-leaf-7466071_jpg.rf.1df72d0f3799461608193a6238027ff2.jpg: 640x608 1 grape leaf, 8.1ms\n", "image 169/246 /content/PlantDoc-1/test/images/ill-tomato-of-tomato-mosaic-virus-DBE17N_jpg.rf.27a980ae0f3828df89acb23b74324375.jpg: 480x640 1 Apple leaf, 2 Tomato leaf mosaic viruss, 1 Tomato leaf yellow virus, 2 Tomato mold leafs, 7.5ms\n", "image 170/246 /content/PlantDoc-1/test/images/image_jpg.rf.fbac91f547164c8d21bb199efecddd40.jpg: 640x640 1 Tomato mold leaf, 8.2ms\n", "image 171/246 /content/PlantDoc-1/test/images/irish-blight-symptoms-on-potato-leaves-atmf8b_jpg.rf.25a5e5aa727c88bb42c354dc7451a452.jpg: 448x640 1 Potato leaf early blight, 1 Tomato leaf late blight, 7.2ms\n", "image 172/246 /content/PlantDoc-1/test/images/iron-deficiency-raspberry-leaf-chlorosis-isolated-32457798_jpg.rf.382da11ebc82f6720acb689b21e99a7e.jpg: 640x576 3 Raspberry leafs, 8.0ms\n", "image 173/246 /content/PlantDoc-1/test/images/late_blight_tomato_leaf_spore1x1200_jpg.rf.00f2407984971fd35d18dd8754630ed3.jpg: 512x640 4 Tomato leaf late blights, 7.1ms\n", "image 174/246 /content/PlantDoc-1/test/images/latest-cb-20100621160325_jpg.rf.81a4a38fcd9b1b95788505f4fc3ae39b.jpg: 640x512 1 grape leaf, 7.0ms\n", "image 175/246 /content/PlantDoc-1/test/images/leaf-blueberry-15281271_jpg.rf.327ab563f731e6f4792cf2365e44e930.jpg: 544x640 9 Blueberry leafs, 7.8ms\n", "image 176/246 /content/PlantDoc-1/test/images/leaf-raspberry-isolated-on-a-white-stock-photography-image-10106222-1625198_jpg.rf.e0cb4ce6a027845edd466f3bfe787b6e.jpg: 640x640 1 Apple leaf, 5 Raspberry leafs, 8.2ms\n", "image 177/246 /content/PlantDoc-1/test/images/leaves-of-raspberry-stock-picture-2529053_jpg.rf.721a68554609b494a77edf076277b85f.jpg: 640x512 3 Raspberry leafs, 1 Strawberry leaf, 7.0ms\n", "image 178/246 /content/PlantDoc-1/test/images/main-qimg-d9c2a93f60568485c989670d9bbf703b_jpg.rf.2e356e93113c620b7d07fea76dbb328f.jpg: 512x640 3 <PERSON><PERSON> leafs, 7.2ms\n", "image 179/246 /content/PlantDoc-1/test/images/northern-highbush-blueberry-blue-huckleberry-vaccinium-corymbosum-EHNXRR_jpg.rf.d7ca7c77be8e990696d5dc5ca158e582.jpg: 640x416 1 Blueberry leaf, 35.7ms\n", "image 180/246 /content/PlantDoc-1/test/images/page_2_jpg.rf.ba7d2cc4d53c99b73d8f39b1176ca4f4.jpg: 544x640 2 Tomato leafs, 1 Tomato mold leaf, 7.8ms\n", "image 181/246 /content/PlantDoc-1/test/images/peach-leaf-10223898_jpg.rf.20f3df2515ca5e9da683a53bc2406b25.jpg: 640x480 1 Peach leaf, 7.1ms\n", "image 182/246 /content/PlantDoc-1/test/images/peach-leaf-16690421_jpg.rf.5fe3a3fd04c752b0632c0b0d0ca33c90.jpg: 480x640 1 Peach leaf, 7.0ms\n", "image 183/246 /content/PlantDoc-1/test/images/peach-leaf-isolated-white-background-42295220_jpg.rf.d75dfd975e2c3457f60be82f2f2976bb.jpg: 512x640 2 Peach leafs, 7.0ms\n", "image 184/246 /content/PlantDoc-1/test/images/pepper-spot_jpg.rf.cf4ac75941bc4e42c7b29c3a2e91223e.jpg: 384x640 1 Bell_pepper leaf spot, 7.0ms\n", "image 185/246 /content/PlantDoc-1/test/images/pepper_bacterial-spot_03_zoom_jpg.rf.2cdc957774ad0c323fb6725a82bd7794.jpg: 448x640 2 Bell_pepper leaf spots, 8.7ms\n", "image 186/246 /content/PlantDoc-1/test/images/pepper_leaf_jpg.rf.62aac848f5edf850c756467bf11b4090.jpg: 640x480 1 Peach leaf, 8.1ms\n", "image 187/246 /content/PlantDoc-1/test/images/photo4_jpg.rf.a27841012b38c15b13ad95596e613344.jpg: 640x640 2 Squash Powdery mildew leafs, 8.1ms\n", "image 188/246 /content/PlantDoc-1/test/images/phytophthora-infestans-late-blight-or-potato-blight-on-the-haulm-of-E29H6G_jpg.rf.0738b7aa47c4c15bb42662812f55ef77.jpg: 640x416 2 Potato leaf early blights, 7.1ms\n", "image 189/246 /content/PlantDoc-1/test/images/phytophthora-infestans-late-blight-or-potato-blight-on-the-haulm-of-EHW1RD_jpg.rf.9905f6ae5bcf48c5c8ae65429747352d.jpg: 640x416 3 Potato leaf late blights, 6.5ms\n", "image 190/246 /content/PlantDoc-1/test/images/phytophthora-infestans-potato-blight-first-foliar-symptoms-on-solanum-EJKRCK_jpg.rf.d23754b26a9e631b238593a64c31e2ef.jpg: 640x416 2 Blueberry leafs, 1 Potato leaf early blight, 1 Potato leaf late blight, 6.7ms\n", "image 191/246 /content/PlantDoc-1/test/images/piano-20gully-20spray-20seed-20damage-2006-20-289-29_JPG_jpg.rf.23093ac05fe05905bd5665cd8f23e761.jpg: 480x640 1 grape leaf, 7.3ms\n", "image 192/246 /content/PlantDoc-1/test/images/pix053_jpg.rf.23e8b9d0f6b652da92be8e1e16c62511.jpg: 640x544 (no detections), 9.7ms\n", "image 193/246 /content/PlantDoc-1/test/images/plant-fruit-berry-leaf-flower-food-herb-produce-blueberry-botany-flora-plants-wildflower-shrub-macro-photography-wipes-flowering-plant-bilberry-huckleberry-dayflower-land-plant-dayflower-family-1090218_jpg.rf.aa8a450bb6e5b94ff048940013ae6ba7.jpg: 448x640 19 Blueberry leafs, 7.7ms\n", "image 194/246 /content/PlantDoc-1/test/images/plant-rust-southern-rust-on-corn-leaf-plant-diseases-rust-of-wheat_jpg.rf.823f3a2404212186fdf0eba26541ac73.jpg: 448x640 1 Corn rust leaf, 7.9ms\n", "image 195/246 /content/PlantDoc-1/test/images/potato-blight-phytophthora-infestans-close-up-of-upper-surface-of-BMMRXC_jpg.rf.3e0aa4cd37852841a246ddb0932ea14b.jpg: 480x640 1 Potato leaf late blight, 7.5ms\n", "image 196/246 /content/PlantDoc-1/test/images/potato-early-blight-alternaria-alternata-lesion-on-a-potato-leaf-a1w1em_jpg.rf.e41c80b8df42c9c26b1f1808c3b26ce5.jpg: 608x640 3 Potato leaf early blights, 1 Potato leaf late blight, 8.2ms\n", "image 197/246 /content/PlantDoc-1/test/images/potato-late-blight_jpg.rf.e613953b0f25c996d338a2dc28732cdb.jpg: 512x640 2 Apple Scab Leafs, 1 Blueberry leaf, 3 Potato leaf early blights, 1 Potato leaf late blight, 1 Soyabean leaf, 8.4ms\n", "image 198/246 /content/PlantDoc-1/test/images/potatobd001_jpg.rf.d2da5f7c0a5ddf1344dfc35d06e1fe29.jpg: 448x640 1 Tomato Early blight leaf, 7.9ms\n", "image 199/246 /content/PlantDoc-1/test/images/powdery-mildew-erysiphe-plantani-on-young-sycamore-leaves-b774tm_jpg.rf.20c0d389b99e314fba3b89b5a78152f4.jpg: 640x640 2 Squash Powdery mildew leafs, 8.1ms\n", "image 200/246 /content/PlantDoc-1/test/images/powdery-mildew-on-pumpkin-leaves-close-up-2_jpg.rf.ea5b755ca324919801fc32e5bdddee07.jpg: 480x640 2 Squash Powdery mildew leafs, 9.4ms\n", "image 201/246 /content/PlantDoc-1/test/images/powdery-mildew-sphaerotheca-fuliginea-infection-on-pumpkin-leaves-ajdkkx_jpg.rf.415cf3f7181f636ab5a9c09fe62e8f20.jpg: 480x640 2 Squash Powdery mildew leafs, 7.6ms\n", "image 202/246 /content/PlantDoc-1/test/images/prunsero_leaf1_jpg.rf.9fe2878c7cc10ebf4b6900b73f8ebd79.jpg: 640x448 1 Apple Scab Leaf, 1 Apple leaf, 1 Cherry leaf, 7.0ms\n", "image 203/246 /content/PlantDoc-1/test/images/prunsero_leaf2_jpg.rf.f2b79f203df67e9ced35f996e1659820.jpg: 640x448 2 Apple leafs, 7.5ms\n", "image 204/246 /content/PlantDoc-1/test/images/prunus-virginiana-le-g<PERSON><PERSON>hauser_jpg.rf.77f31143951e10f03b422ebf1bda7bbe.jpg: 640x512 1 Cherry leaf, 7.4ms\n", "image 205/246 /content/PlantDoc-1/test/images/red-cherry-tree-leaf-isolated-crisp-white-background-35742030_jpg.rf.4e1faa8a37e182f7b4671ac5f7fa7c7a.jpg: 544x640 1 Blueberry leaf, 7.9ms\n", "image 206/246 /content/PlantDoc-1/test/images/resistance1c_jpg.rf.dd6a8c93eecfe63a8aa87f0720fc4726.jpg: 640x448 1 Soyabean leaf, 7.1ms\n", "image 207/246 /content/PlantDoc-1/test/images/rose_tree_220430_jpg.rf.c35ac18bde33c4aca29490fede687b3d.jpg: 640x640 1 Blueberry leaf, 1 Cherry leaf, 1 Corn rust leaf, 8.3ms\n", "image 208/246 /content/PlantDoc-1/test/images/rsz0803Figure6_jpg.rf.7eb17a29150816acf5f31cd4c964f86c.jpg: 448x640 1 Corn leaf blight, 7.1ms\n", "image 209/246 /content/PlantDoc-1/test/images/sept4disease-019_jpg.rf.be98da872572081aee961f105cd8fe04.jpg: 512x640 1 Potato leaf early blight, 8.9ms\n", "image 210/246 /content/PlantDoc-1/test/images/show_picture_asp-id-aaaaaaaaaaogcqq-w2-420-h2-378-clip-center-420-378-meta-0_jpg.rf.57367a84b485d0fbc65f653e1d2d0345.jpg: 576x640 1 Corn Gray leaf spot, 8.0ms\n", "image 211/246 /content/PlantDoc-1/test/images/squash-leave-w-powd-mild_jpg.rf.5c542a59a9b09790c2e39236aff2f9f9.jpg: 480x640 2 Squash Powdery mildew leafs, 7.4ms\n", "image 212/246 /content/PlantDoc-1/test/images/squashsilver2_JPG_jpg.rf.dd92ccde74588fcce0d310328174434a.jpg: 480x640 2 Squash Powdery mildew leafs, 6.7ms\n", "image 213/246 /content/PlantDoc-1/test/images/stock-photo-blueberry-leaf-closeup-isolated-on-white-306486470_jpg.rf.c9c5da689c228f6ead70348a4b247dfc.jpg: 480x640 2 Blueberry leafs, 6.6ms\n", "image 214/246 /content/PlantDoc-1/test/images/stock-photo-cultivar-marrow-leaf-strongly-affected-with-a-powdery-mildew-in-the-summer-garden-707948062_jpg.rf.35d9a507faffdf3b194380c4df26ac3a.jpg: 480x640 2 Squash Powdery mildew leafs, 7.5ms\n", "image 215/246 /content/PlantDoc-1/test/images/stock-photo-fresh-soy-leaves-isolated-on-white-94266130_jpg.rf.70fae09f8ec23ced71b76437b00f201b.jpg: 640x448 2 Soyabean leafs, 7.1ms\n", "image 216/246 /content/PlantDoc-1/test/images/stock-photo-fresh-soy-leaves-isolated-on-white-94700095_jpg.rf.eece97daf06bc7eb626c24c5addebe95.jpg: 640x640 3 Soyabean leafs, 8.1ms\n", "image 217/246 /content/PlantDoc-1/test/images/stock-photo-green-blueberry-leaf-isolated-with-clipping-path-550344304_jpg.rf.2dfa325c138b235f48dfd7253dd83030.jpg: 640x480 1 Apple leaf, 7.1ms\n", "image 218/246 /content/PlantDoc-1/test/images/stock-photo-peach-leaf-isolated-on-white-background-281097503_jpg.rf.cd1c153a53a40d821f26fe25aa2d0f32.jpg: 416x640 1 Peach leaf, 7.1ms\n", "image 219/246 /content/PlantDoc-1/test/images/stock-photo-peach-tree-leaf-isolated-over-white-background-back-side-64472731_jpg.rf.832a1dabcc8eb575f7925e3c3bfdbeb8.jpg: 640x608 1 Peach leaf, 9.8ms\n", "image 220/246 /content/PlantDoc-1/test/images/stock-photo-soy-leaf-isolated-on-white-background-120961801_jpg.rf.5ac0da17c49efeef8db7e75befba52fd.jpg: 608x640 3 Soyabean leafs, 8.2ms\n", "image 221/246 /content/PlantDoc-1/test/images/stock-photo-two-sides-of-blueberry-leaf-isolated-on-white-15582232_jpg.rf.92a79a697f60a59f4599698ea97455f6.jpg: 576x640 2 Blueberry leafs, 8.1ms\n", "image 222/246 /content/PlantDoc-1/test/images/strawberry-leaf-22360816_jpg.rf.42b7e38516bbacfad287d9e291fe7894.jpg: 608x640 3 Strawberry leafs, 8.1ms\n", "image 223/246 /content/PlantDoc-1/test/images/strawberry-leaf-stock-photo-1431216_jpg.rf.829e8468ae82f6ea5a5b801df7bebc26.jpg: 640x640 3 Strawberry leafs, 8.2ms\n", "image 224/246 /content/PlantDoc-1/test/images/strawberry-leaves-collection-white-background-36684676_jpg.rf.b95ca3d453519b4ae2864316b3243d33.jpg: 480x640 7 Strawberry leafs, 8.6ms\n", "image 225/246 /content/PlantDoc-1/test/images/strawberry-leaves-isolated-white-17473157_jpg.rf.0029c82eacd945794934136efc8b0e9b.jpg: 640x640 6 Strawberry leafs, 8.2ms\n", "image 226/246 /content/PlantDoc-1/test/images/strawberry-leaves-isolated-white-34722259_jpg.rf.521a6fd2d8e8bcd4758725300eac6fa2.jpg: 640x608 3 Strawberry leafs, 8.3ms\n", "image 227/246 /content/PlantDoc-1/test/images/strawberry-leaves-stock-image-948674_jpg.rf.85b55260b58d0126979f7188558dbc76.jpg: 512x640 3 Strawberry leafs, 7.8ms\n", "image 228/246 /content/PlantDoc-1/test/images/strawberry-leaves-stock-picture-948708_jpg.rf.c18d21395637277fdb313ada26d289b1.jpg: 512x640 3 Strawberry leafs, 7.4ms\n", "image 229/246 /content/PlantDoc-1/test/images/strom5_jpg.rf.b4afc7d4bbc1486378d61feba93f11a2.jpg: 448x640 1 Corn leaf blight, 7.3ms\n", "image 230/246 /content/PlantDoc-1/test/images/summersquashpowderymildew_jpg.rf.9a677ab7dcff6042b4e071656329bb4b.jpg: 512x640 1 Squash Powdery mildew leaf, 7.4ms\n", "image 231/246 /content/PlantDoc-1/test/images/surface_jpg.rf.c6b02b4a94cddab544a6365d91d0a383.jpg: 512x640 1 Squash Powdery mildew leaf, 1 Tomato mold leaf, 6.4ms\n", "image 232/246 /content/PlantDoc-1/test/images/three-raspberry-green-leaves-isolated-white-background-62022932_jpg.rf.5e1abbceb986165267d922d00430686e.jpg: 640x608 1 Raspberry leaf, 2 Strawberry leafs, 9.4ms\n", "image 233/246 /content/PlantDoc-1/test/images/three-vibrant-leaves-bird-cherry-tree-13905898_jpg.rf.d7210955e94cb3d763ea452d320fef2c.jpg: 480x640 3 Cherry leafs, 1 Peach leaf, 7.7ms\n", "image 234/246 /content/PlantDoc-1/test/images/tmv-tomato-34_jpg.rf.6fb2ad06bea6437764b98f328a6ca54b.jpg: 480x640 1 Bell_pepper leaf, 1 Tomato leaf, 2 Tomato mold leafs, 7.6ms\n", "image 235/246 /content/PlantDoc-1/test/images/tmv-tomato-9_jpg.rf.9ea785df63b9d18cdbc024bc6b0f421c.jpg: 480x640 (no detections), 6.8ms\n", "image 236/246 /content/PlantDoc-1/test/images/toma_TMV_jpg.rf.594046674df83297a4dcc5b3eb4f28d1.jpg: 448x640 4 Tomato leaf late blights, 1 Tomato leaf yellow virus, 7.5ms\n", "image 237/246 /content/PlantDoc-1/test/images/tomato_V8_jpg.rf.947ba852bff0fb47c0ebc6fa43eb5d90.jpg: 480x640 2 Tomato leaf bacterial spots, 1 Tomato mold leaf, 7.3ms\n", "image 238/246 /content/PlantDoc-1/test/images/tomato_bacterial-speck_01_zoom_jpg.rf.d950f45d7c5797e43e9295e93c87ede2.jpg: 480x640 1 Tomato leaf bacterial spot, 7.7ms\n", "image 239/246 /content/PlantDoc-1/test/images/tomato_early-blight_01_zoom_jpg.rf.4abc81280bccdbfdcf60e99af0783240.jpg: 640x448 1 Tomato Septoria leaf spot, 1 Tomato leaf bacterial spot, 1 Tomato leaf late blight, 7.3ms\n", "image 240/246 /content/PlantDoc-1/test/images/tomato_virus_04_zoom_jpg.rf.7c8720ece1216441a62cf9928f2d3047.jpg: 480x640 2 Tomato leafs, 7.1ms\n", "image 241/246 /content/PlantDoc-1/test/images/tylcv-seminar-1-638_jpg.rf.71ccaa51ec95c1ac4724a3230f3858ad.jpg: 512x640 10 Tomato leaf yellow viruss, 9.6ms\n", "image 242/246 /content/PlantDoc-1/test/images/vaccinium-angustifolium-low-bush-blueberry_0830_124221_jpg.rf.6da5ea8e7c34fcb4995c922b7a9eb097.jpg: 480x640 1 Blueberry leaf, 2 Cherry leafs, 7.2ms\n", "image 243/246 /content/PlantDoc-1/test/images/vaccinium_angustifolium_leaf2_JPG_jpg.rf.2341c792ab5f485672aa565de5d787ae.jpg: 640x448 1 Blueberry leaf, 1 Soyabean leaf, 7.0ms\n", "image 244/246 /content/PlantDoc-1/test/images/vitis-riparia-le-ah<PERSON>-b_jpg.rf.cbff18ddf7cd58cbf9f50df2a353a7f9.jpg: 544x640 2 Blueberry leafs, 2 grape leafs, 7.9ms\n", "image 245/246 /content/PlantDoc-1/test/images/why-are-my-pepper-plants-yellow-yellow-pepper-plants-yellow-leaves-green-veins-pepper-plants-yellow-veins_jpg.rf.8a075997f5f1c13ae96a2d5294551c55.jpg: 640x384 5 Bell_pepper leafs, 1 Peach leaf, 7.2ms\n", "image 246/246 /content/PlantDoc-1/test/images/wild-strawberry-leaf-top-view-rotated-KTBXCY_jpg.rf.e10b7efb9903021e933dbc48c69acaee.jpg: 576x640 3 Strawberry leafs, 7.9ms\n", "Speed: 2.7ms preprocess, 11.5ms inference, 1.5ms postprocess per image at shape (1, 3, 576, 640)\n", "Results saved to \u001b[1mruns/detect/predict\u001b[0m\n"]}]}, {"cell_type": "code", "source": [], "metadata": {"id": "NM1K4lnWMQgg"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": [], "metadata": {"id": "yAwgms8GMQeC"}, "execution_count": null, "outputs": []}, {"cell_type": "code", "source": [], "metadata": {"id": "ZpnrKEVlMQVB"}, "execution_count": null, "outputs": []}]}